<!-- <div class="toolbar w-full px-gutter py-3 formsHeader" fxLayout="row" fxLayoutAlign="space-between center" exaiContainer>
  <a class="ltr:mr-4 rtl:ml-4 block" fxLayout="column" fxLayoutAlign="start start">
    <img class="w-48 select-none" src="assets/img/demo/logo.svg">
  </a>
  <div class="ltr:mr-4 rtl:ml-4 block flagLogo" fxLayout="column" fxLayoutAlign="end end">
    <img class="w-5 select-none" src="assets/img/demo/US.svg">
  </div>
</div> -->
<ng-container *ngIf="loadingObs | async">
  <div class="card-div" fxLayout="column" fxLayoutAlign="center center">
      <div class="spinner w-full">
          <div class="bounce1"></div>
          <div class="bounce2"></div>
          <div class="bounce3"></div>
      </div>
  </div>
</ng-container>
<!-- <div class="chat">
  <button mat-raised-button (click)="redirectToCredentialTicketForm()" class="ButtonChat">Sign Up</button>
</div> -->
<div class="chat">
  <button mat-icon-button (click)="redirectToCredentialTicketForm()" class="ButtonChat"  matTooltip=
  "Email Support">
  <mat-icon>email</mat-icon>
    <!-- Sign Up -->
  </button>
</div>
<div class="w-full h-full bg-pattern" fxLayout="column" fxLayoutAlign="center center">
  <div class="card overflow-hidden w-full loginFormWidth max-w-xs">
    <div class="loginFormLogo" fxLayout="column" fxLayoutAlign="center center">
      <div class="fill-current text-center">
        <img class="w-56" src="assets/img/demo/logo.svg">
      </div>
    </div>

    <form [formGroup]="form" class="loginForm" fxLayout="column">
      <div fxFlex="auto" fxLayout="column" class="loginInput">
        <mat-form-field fxFlex="grow" class="example-full-width" appearance="outline">
          <mat-label>{{global.email}}</mat-label>
          <input type="email" class="form-control" matInput placeholder="{{global.email_placeholder}}"
            formControlName="email"
            [ngClass]="{'is-invalid': (!email.valid && email.touched), 'is-valid': (email.valid && email.touched)}" />
          <mat-icon matSuffix>person_outline</mat-icon>
          <mat-error *ngFor="let validation of validation_messages.email">
            <mat-error class="error-message"
              *ngIf="form.get('email').hasError(validation.type) && (form.get('email').dirty || form.get('email').touched)">
              {{validation.message}}</mat-error>
          </mat-error>
        </mat-form-field>

        <mat-form-field fxFlex="grow" class="example-full-width" appearance="outline" class="eyeIcon">
          <mat-label>{{global.password}}</mat-label>
          <input type="password" class="form-control" matInput placeholder="{{global.pwd_placeholder}}"
            formControlName="password" [type]="hide ? 'password' : 'text'"
            [ngClass]="{'is-invalid': (!password.valid && password.touched), 'is-valid': (password.valid && password.touched)}" />
          <mat-icon mat-icon-button matSuffix (click)="togglePwd()" [attr.aria-label]="'Hide password'"
            [attr.aria-pressed]="hide">{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
          <mat-error *ngFor="let validation of validation_messages.password">
            <mat-error class="error-message"
              *ngIf="form.get('password').hasError(validation.type) && (form.get('password').dirty || form.get('password').touched)">
              {{validation.message}}</mat-error>
          </mat-error>
        </mat-form-field>
      </div>

      <div *ngIf="!showRoleDropdown" fxLayout="row" fxLayoutAlign="end">
        <a [routerLink]="['/forgot']" class="m-px">{{global.forgot_Pwd}}</a>
      </div>

      <button *ngIf="!showRoleDropdown" class="loginBtn mt-4" (click)="login()" (keydown)="keyDownFunction($event)" mat-raised-button
        type="button" [disabled]="!form.valid || loginBtnClicked">
        {{global.login}}
      </button>

      <div *ngIf="showRoleDropdown">
        <div class="role-banner">
          <div class="line"></div>
          <div class="text">Choose your role to continue</div>
          <div class="line"></div>
        </div>
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Choose role</mat-label>
          <mat-select>
            <mat-option *ngFor="let role of consolidatedRoles" [value]="role.roleId" (click)="SelectRole(role, loginData.idToken, loginData.stateId)">{{role.roleName}}</mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <!-- <div class="more">
        <span><span>{{global.Or_SignIn_With}}</span></span>
      </div>
      <div class="more-btn">
        <img src="assets/img/demo/user.svg" class="img-fluid">
        <img src="assets/img/demo/biometric.svg" class="img-fluid">
        <img src="assets/img/demo/voice.svg" class="img-fluid">
      </div> -->

      <div *ngIf="showSignUpLink" class="haveAccount" fxLayout="row" fxLayoutAlign="center">
        {{global.Dont_Have_an_Account}}<a class="signUp" [routerLink]="'/register'" [queryParams]="{ RoleId: roleIdfromQueryParams, TenantCode: tenantCodefromQueryParams, StateCode: stateCodefromQueryParams}">&nbsp;{{global.signUp}}</a>
      </div>
    </form>
  </div>
</div>


<ng-template #loading>

</ng-template>
<exai-footer class="exai-footer" style="position: absolute;"></exai-footer>

<!--  [queryParams]="{ RoleId: '2', TenantCode: 'EX', StateCode: 'PA'}" -->