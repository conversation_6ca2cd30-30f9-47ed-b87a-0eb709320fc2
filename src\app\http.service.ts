import { HttpBackend, HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment } from "src/environments/environment";
import {
  MatSnackBar,
  MatSnackBarHorizontalPosition,
} from "@angular/material/snack-bar";
import { SnackbarComponent } from "../app/authorization/snackbar/snackbar.component";
import { AuthorizationService } from "./authorization/authorization.service";
import { catchError } from "rxjs/operators";
import { Observable, throwError } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class HttpService {
  emailIdforFrgtPwd: any;
  RoleId?: number;
  TenantCode?: any;
  StateId?: number;
  horizontalPosition: MatSnackBarHorizontalPosition = "end";
  roleID: number = 1;
  id: number = 0;
  createdBy: number = 6;
  tenantId: number = 3;
  constructor(
    private http: HttpClient,
    private https: HttpBackend,
    private _snackBar: MatSnackBar,
    private _authorizationService: AuthorizationService
  ) {}

  login(loginDetails: any) {
    var url = `${environment.apiUrl}signin`;
    return this.http.post(url, loginDetails);
  }

  //login with multi factor authentication (MFA)
  customLogin(loginDetails: any) {
    var url = `${environment.apiUrl}customsignin`;
    return this.http.post(url, loginDetails);
  }

  forgotPwd(inputDetails: any) {
    var url = `${environment.apiUrl}forgetPassword?username=${inputDetails.username}`;
    return this.http.post(url, inputDetails);
  }

  confirmMFA(inputDetails: any) {
    var url = `${environment.apiUrl}confirmMFA`;
    return this.http.post(url, inputDetails);
  }

  reset(inputDetails: any) {
    var url = `${environment.apiUrl}resetPassword`;
    return this.http.post(url, inputDetails);
  }

  resetPwd(resetDetails: any) {
    var url = `${environment.apiUrl}v2/resetPassword`;
    return this.http.post(url, resetDetails);
  }

  // resetPwd(resetDetails:any){
  //   var url = `${environment.apiUrl}passwordReset`;
  //   return this.http.post(url, resetDetails);
  // }

  register(userDetails: any) {
    var url = `${environment.apiUrl}register`;
    return this.http.post(url, userDetails);
  }

  resendVerificationLink(userMailID: any) {
    var url = `${environment.apiUrl}resendVerificationLink?Email=${userMailID.Email}`;
    return this.http.post(url, userMailID);
  }

  forceToChangePwd(inputDetails: any) {
    var url = `${environment.apiUrl}forceToChangePassword`;
    return this.http.post(url, inputDetails);
  }

  getStates() {
    var url = `${environment.apiUrl}states`;
    return this.http.get(url);
  }

  callSnackbaronSuccess(msg: string) {
    this.openSnackBar(msg, "success-snackbar");
  }

  callSnackbaronError(msg: string) {
    this.openSnackBar(msg, "error-snackbar");
  }

  callSnackbaronWarning(msg: string) {
    this.openSnackBar(msg, "warning-snackbar");
  }

  openSnackBar(message: string, panelClass: string) {
    this._snackBar.openFromComponent(SnackbarComponent, {
      data: message,
      panelClass: [panelClass, "wider-snackbar"], // Add a custom CSS class
      // horizontalPosition: this.horizontalPosition,
      duration: 4000,
    });
  }

  getCandidateName(email: string) {
    var url = `${environment.Clientapi}api/GetInfo/get-person-name?EmailId=${email}`;
    return this.http.get(url);
  }
  getStateAgeLimits(clientStateCode: string): Observable<any[]> {
    var url = `${environment.apiUrl}StateExamAgeLimit?clientStateCode=${clientStateCode}`;
    return this.http
      .get<any[]>(url)
      .pipe(catchError((error: any) => throwError(error)));
  }
}
