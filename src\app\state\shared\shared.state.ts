// // import { userDetails } from "../../candiate.types";
// // import { CartItem } from "../../scheduled/state/models/cartItem";

export interface SharedState {
  showLoading: boolean;
  errorMessage: string;
  //   cartItems: CartItem[];
  //   userdetails: userDetails;
}

export const initialState: SharedState = {
  showLoading: false,
  errorMessage: "",
  //   cartItems: [],
  //   userdetails: null,
};
