import { Component, OnInit, HostListener } from "@angular/core";
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  Validators,
} from "@angular/forms";
import { HttpService } from "src/app/http.service";
import { ActivatedRoute, Router } from "@angular/router";
import { GlobalService } from "src/app/global.service";
import { AuthorizationService } from "../authorization.service";
import { Observable } from "rxjs";
import { getLoading } from "src/app/state/shared/shared.selectors";
import { Store } from "@ngrx/store";
@Component({
  selector: "exai-reset",
  templateUrl: "./reset.component.html",
  styleUrls: ["./reset.component.scss"],
})
export class ResetComponent implements OnInit {
  CheckValueNameorProduct: string | null = null;
  public static loginDetails;
  form: FormGroup;

  lastNameevent;
  middleNameevent;
  firstName;
  lastName;
  middleName;
  passwordContainsFullNamePartnew;
  email: AbstractControl;
  password: AbstractControl;
  hide = true;
  accesscode: AbstractControl;
  btnEnabled: any;
  disableNote: boolean = false;
  disableAccessCode: boolean = false;
  resetPwdBtnClicked: boolean = false;
  resetPasswordTest: any;
  paaswordProductName;
  isnicword;
  namePartRegex: any;
  emailHidden: string;
  confirmationCode: string;

  constructor(
    private fb: FormBuilder,
    private services: HttpService,
    private router: Router,
    private route: ActivatedRoute,
    public global: GlobalService,
    private _authorizationService: AuthorizationService,
    private store: Store
  ) {
    const info = _authorizationService.passwordInformation;
    this.disableAccessCode = !this._authorizationService.enableResetFlag;
    // info && 'email' in info ||this._authorizationService.enableResetFlag ? "" : this.router.navigateByUrl('forgot');
  }

  loadingObs: Observable<boolean>;

  validation_messages = {
    accesscode: [
      { type: "required", message: this.global.access_code_is_required },
    ],
    email: [
      { type: "required", message: this.global.Email_is_required },
      { type: "pattern", message: this.global.Enter_a_valid_email },
    ],
    password: [
      { type: "required", message: this.global.Password_is_required },
      { type: "pattern", message: this.global.Use_12_or_more_characters },
      {
        type: "maxlength",
        message: this.global.Password_should_not_exceed_99_characters,
      },
    ],
  };

  ngOnInit() {
    this.loadingObs = this.store.select(getLoading);

    const encodedUser = this.route.snapshot.queryParamMap.get("username");
    const confirmationCode = this.route.snapshot.queryParamMap.get("code");

    if (!encodedUser || !confirmationCode) {
      this.router.navigateByUrl("/forgot");
      return;
    }

    try {
      this.emailHidden = encodedUser;
      this.confirmationCode = confirmationCode;
    } catch (err) {
      this.router.navigateByUrl("/forgot");
      return;
    }

    const email = this._authorizationService.passwordInformation.email;
    this.services.getCandidateName(email).subscribe((data: any) => {
      this.firstName = data.firstName;
      this.lastName = data.lastName;
      this.middleName = data.middleName;
    });
    const info = this._authorizationService.passwordInformation;

    this.form = this.fb.group(
      {
        password: [
          "",
          [
            Validators.required,
            Validators.minLength(12),
            Validators.maxLength(99),
            Validators.pattern(
              "^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{12,}$"
            ),
          ],
        ],
        confirmPassword: [""],
      },
      {
        validator: confirmPassword("password", "confirmPassword"),
      }
    );
    this.accesscode = this.form.controls["accesscode"];
    this.email = this.form.controls["email"];
    this.password = this.form.controls["password"];
  }

  // To check equality of password and confirm password
  // checkStatus(): boolean {
  //   const controls = this.form.controls;
  //   const invalid = [];
  //   for (const name in controls) {
  //     invalid.push({ name: name, valid: !controls[name].invalid });
  //   }
  //   const status = invalid.every((ele: any) => ele.valid == true);
  //   return status;
  // }

  checkStatus(): boolean {
    return this.form.valid;
  }

  paaEventCap($event) {
    this.global.passEv($event);
    this.paaswordProductName = this.global.paaswordProductName;
  }
  paaEventCap1($event) {
    this.global.firstNameevent = this.firstName;
    this.global.lastNameevent = this.lastName;
    this.global.middleNameevent = this.middleName;
    this.global.passTextChecking($event);
    this.passwordContainsFullNamePartnew =
      this.global.passwordContainsFullNamePartnew;
  }

  // Only Integer Numbers for ssn
  keyPressNumbers(event) {
    var charCode = event.which ? event.which : event.keyCode;
    // Only Numbers 0-9
    if (charCode < 48 || charCode > 57) {
      event.preventDefault();
      return false;
    } else {
      return true;
    }
  }
  togglePwd() {
    this.hide = !this.hide;
  }
  @HostListener("window:keydown", ["$event"])
  keyDownFunction(event) {
    if (event.keyCode === 13) {
      this.btnEnabled = document.getElementsByClassName("submitPwd");
      for (let key in this.btnEnabled) {
        if (this.btnEnabled[key].disabled === false) {
          this.resetPwd();
        }
      }
    }
  }

  checkValueTrue(): string | null {
    this.CheckValueNameorProduct = this.passwordContainsFullNamePartnew
      ? "Password should not contain the firstname, middlename, and lastname."
      : this.paaswordProductName
      ? "Password should not contain any product name or words like 'test'."
      : null;

    this.updatePasswordBorder();
    return this.CheckValueNameorProduct;
  }

  private updatePasswordBorder(): void {
    const outlines = document.querySelectorAll(".mat-form-field-outline-thick");
    const passwordFieldOutline = outlines[2] as HTMLElement;

    if (passwordFieldOutline) {
      if (this.CheckValueNameorProduct) {
        passwordFieldOutline.classList.add("Border");
      } else {
        passwordFieldOutline.classList.remove("Border");
      }
    }
  }

  resetPwd(): void {
    this.resetPwdBtnClicked = true;

    const resetDetails = {
      userName: this.emailHidden,
      confirmationCode: this.confirmationCode,
      password: this.form.value.password,
    };

    this.services.resetPwd(resetDetails).subscribe({
      next: (data: any) => {
        if (data?.httpStatusCode === 200) {
          this.services.callSnackbaronSuccess(
            this.global.Password_updated_successfully
          );
          this.router.navigateByUrl("");
        } else {
          this.services.callSnackbaronError(this.global.Please_try_again);
          this.resetPwdBtnClicked = false;
        }
      },
      error: (error) => {
        this.services.callSnackbaronError(
          error?.error || this.global.Please_try_again
        );
        this.resetPwdBtnClicked = false;
      },
    });
  }

  forgotPwd() {
    this.disableAccessCode = true;
    this.router.navigateByUrl("/forgot");
  }
}

const confirmPassword = (controlName: string, matchingControlName: string) => {
  return (formGroup: FormGroup) => {
    const control = formGroup.controls[controlName];
    const matchingControl = formGroup.controls[matchingControlName];

    if (control.value !== matchingControl.value) {
      matchingControl.setErrors({ mustMatch: true });
    } else {
      if (matchingControl.errors) {
        delete matchingControl.errors.mustMatch;
        if (Object.keys(matchingControl.errors).length === 0) {
          matchingControl.setErrors(null);
        }
      }
    }
  };
};
