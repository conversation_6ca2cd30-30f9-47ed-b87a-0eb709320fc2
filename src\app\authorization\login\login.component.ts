import { environment } from '../../../environments/environment';
import { Component, OnInit, HostListener } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpService } from '../../http.service';
import { GlobalService } from '../../global.service';
import { AuthorizationService } from '../authorization.service';
import { Observable } from 'rxjs';
import { getLoading } from 'src/app/state/shared/shared.selectors';
import { Store } from '@ngrx/store';

@Component({
  selector: 'exai-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})

export class LoginComponent implements OnInit {
  public static loginDetails;

  form: FormGroup;
  email: AbstractControl;
  password: AbstractControl;
  hide = true;
  btnEnabled: any;
  loginBtnClicked: boolean = false;
  tenantCodefromQueryParams: any;
  roleIdfromQueryParams: any;
  stateCodefromQueryParams: any;
  showSignUpLink: boolean = false;
  count:any=1;
  noOfAttempts:number;
  showRoleDropdown: boolean = false;
  loginData: any;
  consolidatedRoles: any[] = [];
  constructor(private router: Router,
    private fb: FormBuilder,
    private services: HttpService,
    public global: GlobalService,
    private _authorizationService: AuthorizationService,
    private store: Store, private route: ActivatedRoute,
  ) { }

  validation_messages = {
    'email': [
      { type: 'required', message: this.global.Email_is_required },
      { type: 'pattern', message: this.global.Enter_a_valid_email }
    ],
    'password': [
      { type: 'required', message: this.global.Password_is_required },
      { type: 'pattern', message: this.global.Please_enter_valid_password },
    ]
  };

  loadingObs: Observable<boolean>;
  redirectToCredentialTicketForm(){
    window.location.href= 'assets/img/demo/credentia-ticket-form.html';
    
  }

  ngOnInit() {
    if (location.search) {
      this.route.queryParams
        .subscribe(params => {
          if (params.RoleId) {
            this.roleIdfromQueryParams = params.RoleId;
          }
          if (params.TenantCode) {
            this.tenantCodefromQueryParams = params.TenantCode;
          }
          if (params.StateCode) {
            this.stateCodefromQueryParams = params.StateCode;
            this.showSignUpLink = true;
          }
        }
        );
    }
    this.loadingObs = this.store.select(getLoading)
    // this.loadingObs.subscribe(data=>{
    //   console.log(data)
    // });

    this.form = this.fb.group({
      email: ['', Validators.compose([Validators.required,
      Validators.pattern('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,10}$')])],
      password: ['', [Validators.required
      ]]
    });

    this.email = this.form.controls['email'];
    this.password = this.form.controls['password'];
  }

  togglePwd() {
    this.hide = !this.hide
  }

  @HostListener('window:keydown', ['$event'])

  keyDownFunction(event) {
    if (event.keyCode === 13) {
      this.btnEnabled = document.getElementsByClassName('loginBtn');
      for (let key in this.btnEnabled) {
        if (this.btnEnabled[key].disabled === false) {
          this.login();
        }
      }
    }
  }

  login() {
    

    
    this.loginBtnClicked = true;
    let loginDetails = {
      userName: this.form.value.email,
      password: this.form.value.password,
    
    };
    this.services.login(loginDetails).subscribe((data: any) => {
      if (data) {
        this.showRoleDropdown = false;
        this.loginData = data;
        if(data.stateId==this.global.PAStateId && loginDetails.password.length<12 
            && (data.challengeName=="Success" || data.challengeName=="SELECT_MFA_PREFERRED")){
          this._authorizationService.enableResetFlag=true;
          this._authorizationService.tempPasswordInfo.email = loginDetails.userName;
          this._authorizationService.tempPasswordInfo.tempPwd = loginDetails.password;
          this._authorizationService.accessToken=data.accessToken
          this.router.navigateByUrl('/reset')
        }
        else{
        if(data.challengeName == "NEW_PASSWORD_REQUIRED"){ 
          this._authorizationService.tempPasswordInfo.email = loginDetails.userName;
          this._authorizationService.tempPasswordInfo.tempPwd = loginDetails.password;
          this.services.callSnackbaronSuccess(this.global.Please_change_your_pwd);
          this.router.navigateByUrl('changePassword');
        }
        else if (data.challengeName == "SELECT_MFA_PREFERRED") {
          this._authorizationService.tempPasswordInfo.email = loginDetails.userName;
          this._authorizationService.tempPasswordInfo.password = loginDetails.password;
          this.router.navigateByUrl('selectPreferredMFA')
        }
        else {
          // Always consolidate roles first to handle duplicates
          this.consolidatedRoles = this.consolidateRoles(data.roles);

          if(this.consolidatedRoles.length > 1){
            this.showRoleDropdown = true;
          } else if(this.consolidatedRoles.length === 1) {
            this.SelectRole(this.consolidatedRoles[0], data.idToken, data.stateId);
          }
      }
    }
      } else {
        this.services.callSnackbaronError(this.global.Invalid_credentials_Login_failed);
        this.loginBtnClicked = false;
      }  
    }, err => {
      this.services.callSnackbaronError(err.error);
      this.loginBtnClicked = false;
    });

  }

  consolidateRoles(roles: any[]): any[] {
    const roleMap = new Map();

    roles.forEach(role => {
      const key = role.roleId;
      if (!roleMap.has(key)) {
        // Create a consolidated role entry
        roleMap.set(key, {
          roleId: role.roleId,
          roleName: role.roleName,
          personTenantRoleId: role.personTenantRoleId, // Use the first one as default
          personId: role.personId,
          originalRoles: [role] // Keep track of all original roles for this type
        });
      } else {
        // Add to the list of original roles for this role type
        roleMap.get(key).originalRoles.push(role);
      }
    });

    // Log consolidation for debugging (can be removed in production)
    const consolidated = Array.from(roleMap.values());
    return consolidated;
  }

  SelectRole(role, idToken, stateId): void {
    switch (role.roleId) {
      case this.global.Candidate_RoleID: {
        window.location.href = environment.redirectUrl + environment.candidate + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.global.SuperAdmin_RoleID: {
        window.location.href = environment.redirectUrl + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.global.OperationStaff_RoleID: {
        window.location.href = environment.redirectUrl + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.global.StateClient_RoleID: {
        window.location.href = environment.redirectUrl + environment.client + environment.state +`/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.global.TrainingInstitute_RoleID: {
        // For users with multiple training institutes, use the first one
        // The consolidated role contains the first personTenantRoleId by default
        let roleId = stateId ==this.global.stateId?environment.sponsor:environment.training
        window.location.href = environment.redirectUrl + environment.client + roleId + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.global.Sponser_RoleID: {
        window.location.href = environment.redirectUrl + environment.client + environment.sponsor + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.global.SupportStaff: {
        window.location.href = environment.redirectUrl + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.global.Employer: {
        window.location.href = environment.redirectUrl + environment.client + environment.Employees + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.global.FinanaceRole: {
        window.location.href = environment.redirectUrl + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.global.Evalator: {
        window.location.href = environment.Evalutor + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}&personId=${role.personId}`;
        break;
      }
      case this.global.testsite: {
        window.location.href = environment.redirectUrl + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.global.Proctor: {
        window.location.href =  environment.Evalutor  + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.global.Hrms: {
        window.location.href =  environment.Evalutor  + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.global.operationaccount: {
        window.location.href =  environment.Evalutor  + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.global.PayRole: {
        window.location.href =  environment.Evalutor  + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.global.QAC: {
        window.location.href = environment.redirectUrl + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      default: {
        this.services.callSnackbaronError(this.global.Please_check_your_credentials);
        break;
      }
    }
  }

}
