{"extends": "tslint:recommended", "rules": {"align": {"options": ["parameters", "statements"]}, "array-type": false, "arrow-parens": false, "arrow-return-shorthand": true, "curly": true, "deprecation": {"severity": "warning"}, "component-class-suffix": true, "contextual-lifecycle": true, "directive-class-suffix": true, "directive-selector": [true, "attribute", "exai", "camelCase"], "component-selector": [true, "element", "exai", "kebab-case"], "eofline": true, "import-blacklist": [true, "rxjs/Rx"], "import-spacing": true, "indent": {"options": ["spaces"]}, "interface-name": false, "max-classes-per-file": false, "max-line-length": [true, 140], "member-access": false, "member-ordering": [true, {"order": ["static-field", "instance-field", "static-method", "instance-method"]}], "no-consecutive-blank-lines": false, "no-console": [true, "debug", "info", "time", "timeEnd", "trace"], "no-empty": false, "no-inferrable-types": [true, "ignore-params"], "no-non-null-assertion": true, "no-redundant-jsdoc": true, "no-switch-case-fall-through": true, "no-var-requires": false, "object-literal-key-quotes": [true, "as-needed"], "object-literal-sort-keys": false, "ordered-imports": false, "quotemark": [true, "single"], "semicolon": {"options": ["always"]}, "space-before-function-paren": {"options": {"anonymous": "never", "asyncArrow": "always", "constructor": "never", "method": "never", "named": "never"}}, "trailing-comma": false, "no-conflicting-lifecycle": true, "no-host-metadata-property": false, "no-input-rename": true, "no-inputs-metadata-property": true, "no-output-native": true, "no-output-on-prefix": true, "no-output-rename": true, "no-outputs-metadata-property": true, "template-banana-in-box": true, "template-no-negated-async": true, "typedef-whitespace": {"options": [{"call-signature": "nospace", "index-signature": "nospace", "parameter": "nospace", "property-declaration": "nospace", "variable-declaration": "nospace"}, {"call-signature": "onespace", "index-signature": "onespace", "parameter": "onespace", "property-declaration": "onespace", "variable-declaration": "onespace"}]}, "use-lifecycle-interface": true, "use-pipe-transform-interface": true, "variable-name": {"options": ["allow-pascal-case", "allow-leading-underscore"]}, "whitespace": {"options": ["check-branch", "check-decl", "check-operator", "check-separator", "check-type", "check-typecast"]}}, "rulesDirectory": ["codelyzer"]}