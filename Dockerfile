# Use the official Node.js image for the build stage
FROM node:14.17.3 AS build

# Set the working directory
WORKDIR /app

# Clone the submodules
#RUN git submodule update --init --recursive --remote

# Copy the package files and install dependencies
COPY package.json package-lock.json ./
RUN npm cache clean --force && npm install

# Install Angular CLI
RUN npm install -g @angular/cli@11.1.0

# Copy the rest of the application files
COPY . .

# Build the Angular application
RUN npm run build

# Use a different Node.js image for the deployment stage
FROM node:16.15.0

# Install AWS CLI
RUN apt-get update && apt-get install -y awscli
# Install a simple HTTP server to serve the Angular application
RUN npm install -g http-server
# Set the working directory
WORKDIR /app

# Copy the build artifacts from the build stage
COPY --from=build /app/dist/exai/ /app/dist

# Expose port 80 for serving the application locally
EXPOSE 80

# Default command to serve the application locally
CMD ["http-server", "/app/dist", "-p", "80"]