<ng-container *ngIf="loadingObs | async">
    <div class="card-div" fxLayout="column" fxLayoutAlign="center center">
        <div class="spinner w-full">
            <div class="bounce1"></div>
            <div class="bounce2"></div>
            <div class="bounce3"></div>
        </div>
    </div>
  </ng-container>
  <div class="w-full h-full bg-pattern dialog" fxLayout="column" fxLayoutAlign="center center">
    <div class="toolbar w-full px-gutter py-3 formsHeader" fxLayout="row" fxLayoutAlign="space-between center"
      exaiContainer>
      <a class="ltr:mr-4 rtl:ml-4 block" fxLayout="column" fxLayoutAlign="start start">
        <img class="w-48 select-none" src="assets/img/demo/logo.svg">
      </a>
      <div class="ltr:mr-4 rtl:ml-4 block flagLogo" fxLayout="column" fxLayoutAlign="end end">
        <img class="w-5 select-none" src="assets/img/demo/US.svg">
      </div>
    </div>
    <div class="card overflow-hidden w-full loginFormWidth max-w-xs">
      <div class="loginFormLogo" fxLayout="column" fxLayoutAlign="center center">
        <div class="fill-current text-center">
          <h3>{{global.Confirm_code}}</h3>
        </div>
      </div>
      <div class="text-center pb-6">
        <h4 class="body-2 text-secondary m-0">{{global.Verification_Code_Message}}</h4>
      </div>
  
      <div [formGroup]="form" class="loginForm pb-3" fxLayout="column">
        <div fxFlex="auto" fxLayout="column" class="loginInput">
          <mat-form-field fxFlex="grow" class="example-full-width" appearance="outline">
            <mat-label>{{global.Verification_Code}}</mat-label>
            <input type="text" class="form-control" matInput placeholder="{{global.Verification_Code}}" formControlName="confirmationCode"
              [ngClass]="{'is-invalid': (!confirmationCode.valid && confirmationCode.touched), 'is-valid': (confirmationCode.valid && confirmationCode.touched)}">
            <mat-error *ngFor="let validation of validation_messages.confirmationCode">
              <mat-error class="error-message"
                *ngIf="form.get('confirmationCode').hasError(validation.type) && (form.get('confirmationCode').dirty || form.get('confirmationCode').touched)">
                {{validation.message}}</mat-error>
            </mat-error>
          </mat-form-field>
        </div>
  
        <button class="loginBtn" mat-raised-button type="button" (click)="confirmMFA();"
          (keydown)="keyDownFunction($event)" [disabled]="!form.valid || submitBtnClicked">
          {{global.Submit}}
        </button>
  
        <div fxLayout="row" fxLayoutAlign="center center">
          <a [routerLink]="['']" class="pt-4 m-px" fxLayout="row" fxLayoutAlign="center center">
            <mat-icon class="leftArrow">keyboard_arrow_left</mat-icon> {{global.Back_to_Sign_In}}
          </a>
        </div>
      </div>
    </div>
    <exai-footer class="exai-footer" style="position: absolute;"></exai-footer>
  </div>