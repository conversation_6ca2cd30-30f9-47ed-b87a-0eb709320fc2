// import { Component, OnInit } from '@angular/core';
// import { Router, ActivatedRoute } from '@angular/router';
// import { GlobalService } from 'src/app/global.service';
// import { HttpService } from 'src/app/http.service';
// import { environment } from 'src/environments/environment';
// import { AuthorizationService } from '../authorization.service';

// @Component({
//   selector: 'exai-confirm-user',
//   templateUrl: './confirm-user.component.html',
//   styleUrls: ['./confirm-user.component.scss']
// })

// export class ConfirmUserComponent implements OnInit {
//   constructor(public global: GlobalService, private route: ActivatedRoute, private router: Router, private services: HttpService, private _authorizationService: AuthorizationService
//     ) { 
//     // const info = _authorizationService.clientId && _authorizationService.confirmationCode && _authorizationService.userName;
//     // info ? this.router.navigateByUrl('confirmUser') : this.router.navigateByUrl('');
//   }
//   ngOnInit(): void {
//     const bc = new BroadcastChannel('test_channel');
//     this.route.queryParams
//       .subscribe(params => {
//         this._authorizationService.clientId = params.client_id;
//         this._authorizationService.userName = params.user_name;
//         this._authorizationService.confirmationCode = params.confirmation_code;
//       }
//       );
//     if (this._authorizationService.clientId && this._authorizationService.userName && this._authorizationService.confirmationCode) {
//       const hasConfirmUserKey = sessionStorage.getItem('confirmUser');
 
//       if(hasConfirmUserKey){
//         // bc.postMessage('false');
//         // sessionStorage.removeItem("confirmUser");
//         this.services.callSnackbaronWarning(this.global.Your_email_is_already_verified_Continue_Login);
//       } else{
//         bc.postMessage('true');
//         sessionStorage.setItem("confirmUser", "true");;
//       }
//     }

//   }
// }
