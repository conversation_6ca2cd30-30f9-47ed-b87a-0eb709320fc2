<ng-container *ngIf="loadingObs | async">
    <div class="card-div" fxLayout="column" fxLayoutAlign="center center">
        <div class="spinner w-full">
            <div class="bounce1"></div>
            <div class="bounce2"></div>
            <div class="bounce3"></div>
        </div>
    </div>
  </ng-container>
<div class="w-full h-full bg-pattern" fxLayout="column" fxLayoutAlign="center center">

    <div class="toolbar w-full px-gutter py-3 formsHeader" fxLayout="row" fxLayoutAlign="space-between center"
        exaiContainer>
        <a class="ltr:mr-4 rtl:ml-4 block" fxLayout="column" fxLayoutAlign="start start">
            <img class="w-48 select-none" src="assets/img/demo/logo.svg">
        </a>
        <div class="ltr:mr-4 rtl:ml-4 block flagLogo" fxLayout="column" fxLayoutAlign="end end">
            <img class="w-5 select-none" src="assets/img/demo/US.svg">
        </div>
    </div>

    <div class="card overflow-hidden w-full loginFormWidth max-w-xs">
        <div class="loginFormLogo" fxLayout="column" fxLayoutAlign="center center">
            <div class="fill-current text-center">
                <h3>{{global.Change_Password}}</h3>
            </div>
        </div>

        <div [formGroup]="form" class="loginForm" fxLayout="column">
            <div fxFlex="auto" fxLayout="column" class="loginInput registerInput">
                <mat-form-field fxFlex="grow" class="example-full-width" appearance="outline">
                    <mat-label>{{global.email}}</mat-label>
                    <input type="email" readonly class="form-control" matInput
                        placeholder="{{global.email_placeholder}}" formControlName="email"
                        [ngClass]="{'is-invalid': (!email.valid && email.touched), 'is-valid': (email.valid && email.touched)}">
                    <mat-error *ngFor="let validation of validation_messages.email">
                        <mat-error class="error-message"
                            *ngIf="form.get('email').hasError(validation.type) && (form.get('email').dirty || form.get('email').touched)">
                            {{validation.message}}</mat-error>
                    </mat-error>
                </mat-form-field>
                <mat-form-field fxFlex="grow" class="example-full-width" appearance="outline">
                    <mat-label>{{global.Old_Password}}</mat-label>
                    <input type="password" readonly class="form-control" matInput
                        placeholder="{{global.pwd_placeholder}}" formControlName="oldpwd"
                        [ngClass]="{'is-invalid': (!oldpwd.valid && oldpwd.touched), 'is-valid': (oldpwd.valid && oldpwd.touched)}">
                    <mat-error *ngFor="let validation of validation_messages.oldpwd">
                        <mat-error class="error-message"
                            *ngIf="form.get('oldpwd').hasError(validation.type) && (form.get('oldpwd').dirty || form.get('oldpwd').touched)">
                            {{validation.message}}</mat-error>
                    </mat-error>
                </mat-form-field>
               
                <mat-form-field fxFlex="grow" class="example-full-width" appearance="outline">
                    <mat-label>{{global.New_Password}}</mat-label>
                    <input type="password" (keyup)="passwordPolicy($event)" class="form-control" (input)="passwordNamePolicy($event)" matInput placeholder="{{global.pwd_placeholder}}"
                        formControlName="password">
                    <mat-error *ngFor="let validation of validation_messages.password">
                        <mat-error class="error-message"
                            *ngIf="form.get('password').hasError(validation.type) && (form.get('password').dirty || form.get('password').touched)">
                            {{validation.message}}</mat-error>
                    </mat-error>
                </mat-form-field>
                <div  class="error_font_size  font-medium mt-4 ml-2 w-48" *ngIf=" form.value.password != '' " >
                    {{checkValueTrue()}} 
                 </div>
                <mat-form-field fxFlex="grow" class="example-full-width" appearance="outline">
                    <mat-label>{{global.Confirm_Password}}</mat-label>
                    <input type="password" class="form-control" matInput placeholder="{{global.pwd_placeholder}}"
                        formControlName="confirmPassword">
                    <mat-error class="error-message"
                        *ngIf="form.get('confirmPassword').touched && !form.get('confirmPassword').valid">
                        {{global.Passwords_not_matched}}
                    </mat-error>
                </mat-form-field>
            </div>

            <button mat-raised-button class="loginBtn mb-2 submitPwd" (click)="changePwd()"
                (keydown)="keyDownFunction($event)" [disabled]="!checkStatus() || submitBtnClicked||paaswordProductName==true || passwordContainsFullNamePartnew==true">
                {{global.Submit}}</button>
            <!-- <button mat-raised-button class="loginBtn" (click)="forgotPwd()">{{global.Back}}</button> -->
        </div>
    </div>

    <exai-footer class="exai-footer" style="position: absolute;"></exai-footer>

</div>