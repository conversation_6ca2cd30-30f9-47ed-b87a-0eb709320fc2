import { Directive, Input, OnChanges, SimpleChanges  } from '@angular/core';
import { MatDatepicker, MatDatepickerInputEvent } from '@angular/material/datepicker';
import { MatDatepickerInput } from '@angular/material/datepicker';
@Directive({
  selector: '[exaiDateFilter]'
})
export class DateFilterDirective {
    @Input() maxDate: Date;

  constructor(private matDatepickerInput: MatDatepickerInput<any>) { }


  ngOnChanges(changes: SimpleChanges): void {
    if (changes.maxDate && this.matDatepickerInput) {
      this.matDatepickerInput._datepicker.registerInput(this.matDatepickerInput);
    }
  }

  dateFilter = (date: Date): boolean => {
    return date >= this.maxDate;
  };
}