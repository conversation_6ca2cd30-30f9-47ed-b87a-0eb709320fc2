import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../shared-module.module';
import { RegisterRoutingModule } from './register-routing.module';
import { RegisterComponent } from './register.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TermsDialogModule } from '../register/terms-dialog/terms-dialog.module'
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { DatePipe } from '@angular/common'
import { ContainerModule } from 'src/@exai/directives/container/container.module';
import { PageLayoutModule } from 'src/@exai/components/page-layout/page-layout.module';
import { NgxMatIntlTelInputModule } from 'ngx-mat-intl-tel-input';
import { FooterModule } from 'src/@exai/layout/footer/footer.module';
import {TooltipModule} from 'ng2-tooltip-directive';
import { SupportComponent } from './support/support.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DateFilterDirective } from './date-filter.directive';

@NgModule({
  declarations: [
    RegisterComponent,
    SupportComponent,
    DateFilterDirective
  ],
  imports: [
    CommonModule,
    RegisterRoutingModule,
    SharedModule,
    FormsModule,
    ReactiveFormsModule,
    FooterModule,
    TooltipModule,
    TermsDialogModule,
    FlexLayoutModule,
    MatSnackBarModule,
    ContainerModule,
    PageLayoutModule,
    NgxMatIntlTelInputModule,
    MatTooltipModule,
  ],
  providers: [
    DatePipe,
  ],
})
export class RegisterModule { }
