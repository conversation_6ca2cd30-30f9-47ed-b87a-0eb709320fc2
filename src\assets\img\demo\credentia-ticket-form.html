<!DOCTYPE html>
<html>
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style>
      .card {
        box-shadow: 0 14px 10px 0 rgba(0, 0, 0, 0.2);
        transition: 0.3s;
        width: 100%;
        height: 44px;
        border-radius: 5px;
        background-color: #eeeeee;
      }

      textarea {
        height: 100px;
      }

      .card_no_shadow {
        box-shadow: 0 0 0;
        transition: 0.3s;
        width: 100%;
        height: 150%;
        border-radius: 5px;
        margin-top: 20px;
      }

      .card:hover {
        box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2);
      }

      img {
        border-radius: 5px 5px 0 0;
      }

      .container {
        padding: 2px 16px;
        /* background-color: #eeeeee; */
      }

      .header_ticket {
        text-align: center;
        float: left;
      }

      .dropbtn {
        background-color: #04aa6d;
        color: white;
        padding: 16px;
        font-size: 16px;
        border: none;
        cursor: pointer;
      }

      .dropbtn:hover,
      .dropbtn:focus {
        background-color: #3e8e41;
      }

      #myInput {
        background-image: url("searchicon.png");
        background-position: 14px 12px;
        background-repeat: no-repeat;
        font-size: 16px;
        padding: 14px 20px 12px 45px;
        border: none;
        border-bottom: 1px solid #ddd;
        border-radius: 3px 3px 0px 0px !important;
      }

      #myInput:focus {
        outline: 1px solid #ddd;
      }

      .dropdown {
        position: relative;
        display: inline-block;
      }

      .dropdown-content {
        /*position: absolute;*/
        width: 245px;
        background-color: #f9f9f9;
        min-width: 214px;
        overflow: auto;
        border: 1px solid #7d7d7d;
        height: 120px;
        z-index: 1;
      }

      .dropdown-content label {
        color: black;
        padding: 2px 16px;
        text-decoration: none;
        display: block;
      }

      .dropdown label:hover {
        background-color: #ddd;
      }

      .show {
        display: block;
      }
    </style>
  </head>

  <body style="margin: 0">
    <div class="card" style="padding-top: 9px">
      <div class="container" style="display: flex">
        <div style="display: flex; width: 33%; text-align: left">
          <img
            src="../demo/logo.svg"
            style="width: 100px; height: 35px"
            alt="ER_Logo"
          />
          <!-- <img style="height: 25px;" src="https://examroom.ai/images/logo_small.svg" alt="ER_Logo"> -->
        </div>
        <div
          style="
            display: flex;
            font-size: 1.5rem;
            width: 33%;
            justify-content: center;
            color: #0076c1;
          "
        >
          Support Ticket Web Form
        </div>
        <!-- <div style="float:right;  width: 33%;text-align: end;"> -->
        <!-- <img src="https://credentia.com/storage/logos/logo_Credentia_tag_Horzi_rgb.svg" style="
		width: 100px;
		height: 25px;" alt="ER_Logo"> -->
        <!-- </div> -->
        <div style="clear: both"></div>
      </div>
    </div>
    <div class="card_no_shadow">
      <div class="container">
        <!DOCTYPE html>
        <html>
          <head>
            <meta
              http-equiv="content-type"
              content="text/html; charset=UTF-8"
            />
            <style>
              #zohoSupportWebToCase textarea,
              #zohoSupportWebToCase input[type="text"],
              #zohoSupportWebToCase select,
              .wb_common {
                width: 235px;
                height: 28px;
              }

              #zohoSupportWebToCase td {
                padding: 11px 5px;
              }

              #zohoSupportWebToCase textarea,
              #zohoSupportWebToCase input[type="text"],
              #zohoSupportWebToCase select {
                border: 1px solid #0076c1;
                padding: 3px 5px;
                border-radius: 4px;
              }

              #zohoSupportWebToCase select {
                box-sizing: unset;
              }

              #zohoSupportWebToCase .wb_selectDate {
                width: auto;
              }

              #zohoSupportWebToCase input.wb_cusInput {
                width: 108px;
              }

              .wb_FtCon {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                margin-top: 15px;
                padding-left: 10px;
              }

              .wb_logoCon {
                display: flex;
                margin-left: 5px;
              }

              .wb_logo {
                max-width: 16px;
                max-height: 16px;
              }

              .zsFormClass {
                background-color: #ffffff;
                width: 600px;
              }
            </style>
            <style>
              .zsFontButton {
                background-color: #0076c1;
                color: #fff;
                border: 1px solid #0076c1;
                border-radius: 4px;
                width: 115px;
                height: 35px;
                font-size: 0.75rem;
                cursor: pointer;
              }
              .zsFontClass {
                color: #000000;
                font-family: Arial;
                font-size: 0.75rem;
              }
            </style>
            <style>
              /* .manfieldbdr {
                        border-left: 1px solid #ff6448 !important
                    } */

              .hleft {
                text-align: left;
              }

              input[type="file"]::-webkit-file-upload-button {
                cursor: pointer;
              }

              .wtcsepcode {
                margin: 0px 15px;
                color: #aaa;
                float: left;
              }

              .wtccloudattach {
                float: left;
                color: #00a3fe !important;
                cursor: pointer;
                text-decoration: none !important;
              }

              .wtccloudattach:hover {
                text-decoration: none !important;
              }

              .wtcuploadinput {
                cursor: pointer;
                float: left;
                width: 62px;
                margin-top: -20px;
                opacity: 0;
                clear: both;
              }

              td:nth-child(even) {
                width: 53%;
              }

              .wtcuploadfile {
                float: left;
                color: #0076c1;
              }

              .filenamecls {
                margin-right: 15px;
                float: left;
                margin-top: 5px;
              }

              .clboth {
                clear: both;
              }

              #zsFileBrowseAttachments {
                clear: both;
                margin: 5px 0px 10px;
              }

              .zsFontClass {
                vertical-align: top;
              }

              #tooltip-zc {
                font: normal 12px Arial, Helvetica, sans-serif;
                line-height: 18px;
                position: absolute;
                padding: 8px;
                margin: 20px 0 0;
                background: #fff;
                border: 1px solid #528dd1;
                -moz-border-radius: 5px;
                -webkit-border-radius: 5px;
                border-radius: 5px;
                color: #eee;
                -webkit-box-shadow: 5px 5px 20px rgba(0, 0, 0, 0.2);
                -moz-box-shadow: 5px 5px 20px rgba(0, 0, 0, 0.2);
                z-index: 10000;
                color: #777;
              }

              .wtcmanfield {
                color: #f00;
                font-size: 16px;
                position: relative;
                top: 2px;
                left: 1px;
              }

              #zsCloudAttachmentIframe {
                width: 100%;
                height: 100%;
                z-index: 99999 !important;
                position: fixed;
                left: 0px;
                top: 0px;
                border-style: none;
                display: none;
                background-color: #fff;
              }

              .wtchelpinfo {
                background-position: -246px -485px;
                width: 15px;
                height: 15px;
                display: inline-block;
                position: relative;
                top: 2px;
                background-image: url("https://css.zohostatic.com/support/5592768/images/zs-mpro.png");
              }

              .zsMaxSizeMessage {
                font-size: 13px;
              }
            </style>
            <script src="https://d17nz991552y2g.cloudfront.net/app/js/jqueryandencoder.ffa5afd5124fbedceea9.js"></script>
            <script>
              function trimBoth(str) {
                return jQuery.trim(str);
              }
              function setAllDependancyFieldsMapping() {
                var mapDependancyLabels = getMapDependenySelectValues(
                  jQuery("[id='property(module)']").val(),
                  "JSON_MAP_DEP_LABELS"
                );
                if (mapDependancyLabels) {
                  for (var i = 0; i < mapDependancyLabels.length; i++) {
                    var label = mapDependancyLabels[i];
                    var obj =
                      document.forms["zsWebToCase_547474000041204025"][label];
                    if (obj) {
                      setDependent(obj, true);
                    }
                  }
                }
              }
              function getMapDependenySelectValues(module, key) {
                var dependencyObj = jQuery.parseJSON(
                  jQuery("[id='dependent_field_values_" + module + "']").val()
                );
                if (dependencyObj == undefined) {
                  return dependencyObj;
                }
                return dependencyObj[key];
              }
              function setDependent(obj, isload) {
                var name = obj.id || (obj[0] && obj[0].id) || "";
                var module = jQuery("[id='property(module)']").val();
                var val = "";
                var myObject = getMapDependenySelectValues(
                  module,
                  "JSON_VALUES"
                );
                if (myObject != undefined) {
                  val = myObject[name];
                }
                var mySelObject = getMapDependenySelectValues(
                  module,
                  "JSON_SELECT_VALUES"
                );
                if (val != null && val != "" && val != "null" && mySelObject) {
                  var fields = val;
                  for (var i in fields) {
                    if (fields.hasOwnProperty(i)) {
                      var isDependent = false;
                      var label = i;
                      var values = fields[i];
                      if (label.indexOf(")") > -1) {
                        label = label.replace(/\)/g, "_____");
                      }
                      if (label.indexOf("(") > -1) {
                        label = label.replace(/\(/g, "____");
                      }
                      if (label.indexOf(".") > -1) {
                        label = label.replace(/\./g, "___");
                      }
                      var depObj =
                        document.forms["zsWebToCase_547474000041204025"][label];
                      if (depObj && depObj.options) {
                        var mapValues = "";
                        var selected_val = depObj.value;
                        var depLen = depObj.options.length - 1;
                        for (var n = depLen; n >= 0; n--) {
                          if (depObj.options[n].selected) {
                            if (mapValues == "") {
                              mapValues = depObj.options[n].value;
                            } else {
                              mapValues =
                                mapValues + ";;;" + depObj.options[n].value;
                            }
                          }
                        }
                        depObj.value = "";
                        var selectValues = mySelObject[label];
                        for (var k in values) {
                          var rat = k;
                          if (rat == "-None-") {
                            rat = "";
                          }
                          var parentValues = mySelObject[name];
                          if (rat == trimBoth(obj.value)) {
                            isDependent = true;
                            depObj.length = 0;
                            var depvalues = values[k];
                            var depLen = depvalues.length - 1;
                            for (var j = 0; j <= depLen; j++) {
                              var optionElement =
                                document.createElement("OPTION");
                              var displayValue = depvalues[j];
                              var actualValue = displayValue;
                              if (actualValue == "-None-") {
                                optionElement.value = "";
                                displayValue = "-None-";
                              } else {
                                optionElement.value = actualValue;
                              }
                              optionElement.text = displayValue;
                              if (mapValues != undefined) {
                                var mapValue = mapValues.split(";;;");
                                var len = mapValue.length;
                                for (var p = 0; p < len; p++) {
                                  if (actualValue == mapValue[p]) {
                                    optionElement.selected = true;
                                  }
                                }
                              }
                              depObj.options.add(optionElement);
                            }
                          }
                        }
                        if (!isDependent) {
                          depObj.length = 0;
                          var len = selectValues.length;
                          for (var j = 0; j < len; j++) {
                            var actualValue = selectValues[j];
                            var optionElement =
                              document.createElement("OPTION");
                            if (actualValue == "-None-") {
                              optionElement.value = "";
                            } else {
                              optionElement.value = selectValues[j];
                            }
                            optionElement.text = selectValues[j];
                            depObj.options.add(optionElement);
                          }
                          depObj.value = selected_val;
                        }
                        if (!isload) {
                          setDependent(depObj, false);
                        }
                        var jdepObj = jQuery(depObj);
                        if (jdepObj.hasClass("select2-offscreen")) {
                          jdepObj.select2("val", jdepObj.val());
                        }
                      }
                    }
                  }
                }
              }
              function setSelectAll(id) {
                var parentElement = document.getElementById(id);
                var hiddenInput = parentElement.querySelector("#hiddenoptions");
                var selectAllElement = parentElement.querySelector(
                  "#selectall" + id
                );
                var selectedValues = [];
                var checkboxes = parentElement.querySelectorAll(
                  ".wb_multi_pick_input"
                );
                checkboxes.forEach(function (cb) {
                  cb.checked = selectAllElement.checked;
                  if (cb.checked && cb.value) {
                    selectedValues.push(cb.value);
                  }
                });
                hiddenInput.value = selectedValues.join(",");
              }
              function setMultiSelectOption(id, obj) {
                var parentElement = document.getElementById(id);
                var hiddenInput = parentElement.querySelector("#hiddenoptions");
                var selectAllElement = parentElement.querySelector(
                  "#selectall" + id
                );
                var selectedStr = hiddenInput.value;
                var selectedValues = selectedStr ? selectedStr.split(",") : [];
                if (obj.checked && obj.value) {
                  selectedValues.push(obj.value);
                } else if (!obj.checked && obj.value) {
                  selectedValues.splice(selectedValues.indexOf(obj.value), 1);
                  selectAllElement.checked = false;
                } else {
                  selectAllElement.checked = false;
                }
                hiddenInput.value = selectedValues.join(",");
              }
              var zctt = (function () {
                var tt,
                  mw = 400,
                  top = 10,
                  left = 0,
                  doctt = document;
                var ieb = doctt.all ? true : false;
                return {
                  showtt: function (cont, wid) {
                    if (tt == null) {
                      tt = doctt.createElement("div");
                      tt.setAttribute("id", "tooltip-zc");
                      doctt.body.appendChild(tt);
                      doctt.onmousemove = this.setpos;
                      doctt.onclick = this.hidett;
                    }
                    tt.style.display = "block";
                    tt.innerHTML = cont;
                    tt.style.width = wid ? wid + "px" : "auto";
                    if (!wid && ieb) {
                      tt.style.width = tt.offsetWidth;
                    }
                    if (tt.offsetWidth > mw) {
                      tt.style.width = mw + "px";
                    }
                    h = parseInt(tt.offsetHeight) + top;
                    w = parseInt(tt.offsetWidth) + left;
                  },
                  hidett: function () {
                    tt.style.display = "none";
                  },
                  setpos: function (e) {
                    var u = ieb
                      ? event.clientY + doctt.body.scrollTop
                      : e.pageY;
                    var l = ieb
                      ? event.clientX + doctt.body.scrollLeft
                      : e.pageX;
                    var cw = doctt.body.clientWidth;
                    var ch = doctt.body.clientHeight;
                    if (l < 0) {
                      tt.style.left = left + "px";
                      tt.style.right = "";
                    } else if (l + w + left > cw) {
                      tt.style.left = "";
                      tt.style.right = cw - l + left + "px";
                    } else {
                      tt.style.right = "";
                      tt.style.left = l + left + "px";
                    }
                    if (u < 0) {
                      tt.style.top = top + "px";
                      tt.style.bottom = "";
                    } else if (u + h + left > ch) {
                      tt.style.top = "";
                      tt.style.bottom = ch - u + top + "px";
                    } else {
                      tt.style.bottom = "";
                      tt.style.top = u + top + "px";
                    }
                  },
                };
              })();
              var zsWebFormMandatoryFields = new Array(
                "First Name",
                "Contact Name",
                "Email",
                "Subject",
                "State",
                "Description"
              );
              var zsFieldsDisplayLabelArray = new Array(
                "First Name",
                "Last Name",
                "Email",
                "Subject",
                "State",
                "Description"
              );
              function zsValidateMandatoryFields2() {
                var name = "";
                var email = "";
                var isError = 0;
                for (
                  var index = 0;
                  index < zsWebFormMandatoryFields.length;
                  index++
                ) {
                  isError = 0;
                  var fieldObject =
                    document.forms["zsWebToCase_547474000041204025"][
                      zsWebFormMandatoryFields[index]
                    ];
                  if (fieldObject) {
                    if (
                      fieldObject.value.replace(/^\s+|\s+$/g, "").length == 0
                    ) {
                      alert(
                        zsFieldsDisplayLabelArray[index] + " cannot be empty "
                      );
                      fieldObject.focus();
                      isError = 1;
                      return false;
                    } else {
                      if (fieldObject.name == "Email") {
                        if (
                          !fieldObject.value.match(
                            /^([\w_][\w\-_.+\'&]*)@(?=.{4,256}$)(([\w]+)([\-_]*[\w])*[\.])+[a-zA-Z]{2,22}$/
                          )
                        ) {
                          isError = 1;
                          alert("Enter a valid email-Id");
                          fieldObject.focus();
                          return false;
                        }
                      }
                    }
                    if (fieldObject.nodeName == "SELECT") {
                      if (
                        fieldObject.options[fieldObject.selectedIndex].value ==
                        "-None-"
                      ) {
                        alert(
                          zsFieldsDisplayLabelArray[index] + " cannot be none"
                        );
                        fieldObject.focus();
                        isError = 1;
                        return false;
                      }
                    }
                    if (fieldObject.type == "checkbox") {
                      if (fieldObject.checked == false) {
                        alert(
                          "Please accept " + zsFieldsDisplayLabelArray[index]
                        );
                        fieldObject.focus();
                        isError = 1;
                        return false;
                      }
                    }
                  }
                }
                if (isError == 0) {
                  if (
                    document.forms["zsWebToCase_547474000041204025"][
                      "zsWebFormCaptchaWord"
                    ].value.replace(/^\s+|\s+$/g, "").length == 0
                  ) {
                    alert("Please enter the captcha code.");
                    document.forms["zsWebToCase_547474000041204025"][
                      "zsWebFormCaptchaWord"
                    ].focus();
                    return false;
                  }
                }
                if (isError == 0) {
                  document
                    .getElementById("zsSubmitButton_547474000041204025")
                    .setAttribute("disabled", "disabled");
                }
              }
              var ZSEncoder = {
                encodeForHTML: function (str) {
                  if (str && typeof str === "string") {
                    return jQuery.encoder.encodeForHTML(str);
                  }
                  return str;
                },
                encodeForHTMLAttribute: function (str) {
                  if (str && typeof str === "string") {
                    return jQuery.encoder.encodeForHTMLAttribute(str);
                  }
                  return str;
                },
                encodeForJavascript: function (str) {
                  if (str && typeof str === "string") {
                    return jQuery.encoder.encodeForJavascript(str);
                  }
                  return str;
                },
                encodeForCSS: function (str) {
                  if (str && typeof str === "string") {
                    return jQuery.encoder.encodeForCSS(str);
                  }
                  return str;
                },
              };
              var zsAttachedAttachmentsCount = 0;
              var zsAllowedAttachmentLimit = 4;
              var zsAttachmentFileBrowserIdsList = [1, 2, 3, 4, 5];
              function zsOpenCloudPickerIframe() {
                if (zsAttachedAttachmentsCount < 5) {
                  var zsCloudPickerIframeSrc = jQuery(
                    "#zsCloudPickerIframeSrc"
                  ).val();
                  jQuery("#zsCloudAttachmentIframe")
                    .attr(
                      "src",
                      zsCloudPickerIframeSrc.substring(
                        0,
                        zsCloudPickerIframeSrc.length - 1
                      ) +
                        (5 - zsAttachedAttachmentsCount)
                    )
                    .show();
                  zsListenCloudPickerMessages();
                }
              }
              function zsListenCloudPickerMessages() {
                if (window.addEventListener) {
                  window.addEventListener(
                    "message",
                    zsWebReceiveMessage,
                    false
                  );
                } else if (window.attachEvent) {
                  window.attachEvent("onmessage", zsWebReceiveMessage);
                }
              }
              var zsCloudPickerJSON = {};
              function zsWebReceiveMessage(event) {
                var zsUrlRegex =
                  /^(ht|f)tp(s?)\:\/\/[0-9a-zA-Z]([-.\w]*[0-9a-zA-Z])*(:(0-9)*)*(\/?)([a-zA-Z0-9\-\.\?\,\:\'\/\\+=&amp;%\$#_]*)?$/;
                if (zsUrlRegex.test(event.origin)) {
                  var zsMessageType = event.data.split("&&&");
                  if (zsMessageType[0] == "zsCloudPickerMessage") {
                    if (window.addEventListener) {
                      window.removeEventListener(
                        "message",
                        zsWebReceiveMessage,
                        false
                      );
                    } else if (window.attachEvent) {
                      window.detachEvent("onmessage", zsWebReceiveMessage);
                    }
                    jQuery("#zsCloudAttachmentIframe").hide();
                    var isAttachedFilesDetails = zsMessageType[1].split("|||");
                    if (isAttachedFilesDetails[0] == "cloudPickerResponse") {
                      var zsCloudPickerAttachmentDetails =
                        isAttachedFilesDetails[1].split("::::");
                      var zsCloudPickerAttachmentsJSON = jQuery.parseJSON(
                        zsCloudPickerAttachmentDetails[0]
                      );
                      if (
                        zsAttachedAttachmentsCount <= zsAllowedAttachmentLimit
                      ) {
                        zsRenderCloudAttachments(zsCloudPickerAttachmentsJSON);
                      } else {
                        alert("You cannot attach more than 5 files");
                        return false;
                      }
                    }
                  }
                }
              }
              function zsRenderCloudAttachments(zsCloudPickerAttachmentsJSON) {
                if (!jQuery.isEmptyObject(zsCloudPickerAttachmentsJSON)) {
                  jQuery.each(
                    zsCloudPickerAttachmentsJSON,
                    function (cloudServiceName, attachments) {
                      var zsAttachmentsArray = [];
                      if (
                        !jQuery.isEmptyObject(
                          zsCloudPickerJSON[cloudServiceName]
                        )
                      ) {
                        zsAttachmentsArray =
                          zsCloudPickerJSON[cloudServiceName];
                      }
                      for (
                        var attachmentsIndex = 0;
                        attachmentsIndex < attachments.length;
                        attachmentsIndex++
                      ) {
                        if (
                          zsAttachedAttachmentsCount <= zsAllowedAttachmentLimit
                        ) {
                          var zsCloudAttachmentsList = "";
                          var attachmentsDetailJSON =
                            attachments[attachmentsIndex];
                          var zsCloudAttachmentName =
                            attachmentsDetailJSON["docName"];
                          var extension = zsCloudAttachmentName
                            .split(".")
                            .pop()
                            .toLowerCase();
                          var unSupportedExtensions = [
                            "ade",
                            "adp",
                            "apk",
                            "appx",
                            "appxbundle",
                            "bat",
                            "cab",
                            "cer",
                            "chm",
                            "cmd",
                            "com",
                            "cpl",
                            "dll",
                            "dmg",
                            "exe",
                            "hlp",
                            "hta",
                            "ins",
                            "iso",
                            "isp",
                            "jar",
                            "js",
                            "jse",
                            "lnk",
                            "mde",
                            "msc",
                            "msi",
                            "msix",
                            "msixbundle",
                            "msp",
                            "mst",
                            "nsh",
                            "pif",
                            "ps1",
                            "pst",
                            "reg",
                            "scr",
                            "sct",
                            "shb",
                            "sys",
                            "tmp",
                            "url",
                            "vb",
                            "vbe",
                            "vbs",
                            "vxd",
                            "wsc",
                            "wsf",
                            "wsh",
                            "terminal",
                          ];
                          if (unSupportedExtensions.indexOf(extension) != -1) {
                            alert(
                              "The file wasn't attached since its extension is not supported."
                            );
                            continue;
                          }
                          zsAttachedAttachmentsCount =
                            zsAttachedAttachmentsCount + 1;
                          var zsCloudAttachmentId =
                            attachmentsDetailJSON["docId"];
                          zsCloudAttachmentId = zsCloudAttachmentId.replace(
                            /\s/g,
                            ""
                          );
                          zsCloudAttachmentsList =
                            '<div class="filenamecls zsFontClass">' +
                            ZSEncoder.encodeForHTML(zsCloudAttachmentName) +
                            '<a id="' +
                            ZSEncoder.encodeForHTMLAttribute(
                              zsCloudAttachmentId
                            ) +
                            '" cloudservice="' +
                            ZSEncoder.encodeForHTMLAttribute(cloudServiceName) +
                            '" class="zscloudAttachment" style="margin-left:10px;" href="javascript:;">X</a> </div>';
                          jQuery("#zsFileBrowseAttachments").append(
                            zsCloudAttachmentsList
                          );
                          zsAttachmentsArray.push(attachmentsDetailJSON);
                          zsCloudPickerJSON[cloudServiceName] =
                            zsAttachmentsArray;
                          jQuery(
                            "input[name='zsCloudPickerAttachments']"
                          )[0].value = JSON.stringify(zsCloudPickerJSON);
                        }
                      }
                    }
                  );
                }
                zsChangeMousePointer();
              }
              jQuery(document)
                .off("click.cAtm")
                .on("click.cAtm", ".zscloudAttachment", function () {
                  var cloudService = jQuery(this).attr("cloudservice");
                  var cloudAttachmentId = jQuery(this).attr("id");
                  var zsCloudAttachmentsArr = zsCloudPickerJSON[cloudService];
                  var isZsCloudAttachmentRemoved = 0;
                  for (
                    var attachmentsIndex = 0;
                    attachmentsIndex < zsCloudAttachmentsArr.length;
                    attachmentsIndex++
                  ) {
                    if (isZsCloudAttachmentRemoved != 1) {
                      jQuery.each(
                        zsCloudAttachmentsArr[attachmentsIndex],
                        function (
                          attachmentsDetailJsonKey,
                          attachmentsDetailJsonValue
                        ) {
                          if (
                            attachmentsDetailJsonKey == "docId" &&
                            attachmentsDetailJsonValue.replace(/\s/g, "") ==
                              cloudAttachmentId
                          ) {
                            var zsAttachmentToBeRemoved = jQuery.inArray(
                              zsCloudAttachmentsArr[attachmentsIndex],
                              zsCloudAttachmentsArr
                            );
                            zsCloudAttachmentsArr.splice(
                              zsAttachmentToBeRemoved,
                              1
                            );
                            isZsCloudAttachmentRemoved = 1;
                          }
                        }
                      );
                    }
                  }
                  jQuery(this).parent().remove();
                  zsAttachedAttachmentsCount = zsAttachedAttachmentsCount - 1;
                  jQuery("input[name='zsCloudPickerAttachments']")[0].value =
                    JSON.stringify(zsCloudPickerJSON);
                  zsChangeMousePointer();
                });
              function zsRenderBrowseFileAttachment(
                zsAttachmentObject,
                zsAttachmentDetails
              ) {
                if (zsAttachmentObject != "") {
                  if (
                    zsAttachmentDetails.files &&
                    zsAttachmentDetails.files[0].size / (1024 * 1024) > 20
                  ) {
                    zsAttachmentDetails.value = "";
                    alert("Maximum allowed file size is 20MB.");
                    return;
                  }
                  if (zsAttachedAttachmentsCount < 5) {
                    var zsFileName = "";
                    if (zsAttachmentObject.indexOf("\\") > -1) {
                      var zsAttachmentDataSplits =
                        zsAttachmentObject.split("\\");
                      var zsAttachmentDataSplitsLen =
                        zsAttachmentDataSplits.length;
                      zsFileName =
                        zsAttachmentDataSplits[zsAttachmentDataSplitsLen - 1];
                    } else {
                      zsFileName = zsAttachmentObject;
                    }
                    var extension = zsFileName.split(".").pop().toLowerCase();
                    var unSupportedExtensions = [
                      "ade",
                      "adp",
                      "apk",
                      "appx",
                      "appxbundle",
                      "bat",
                      "cab",
                      "cer",
                      "chm",
                      "cmd",
                      "com",
                      "cpl",
                      "dll",
                      "dmg",
                      "exe",
                      "hlp",
                      "hta",
                      "ins",
                      "iso",
                      "isp",
                      "jar",
                      "js",
                      "jse",
                      "lnk",
                      "mde",
                      "msc",
                      "msi",
                      "msix",
                      "msixbundle",
                      "msp",
                      "mst",
                      "nsh",
                      "pif",
                      "ps1",
                      "pst",
                      "reg",
                      "scr",
                      "sct",
                      "shb",
                      "sys",
                      "tmp",
                      "url",
                      "vb",
                      "vbe",
                      "vbs",
                      "vxd",
                      "wsc",
                      "wsf",
                      "wsh",
                      "terminal",
                    ];
                    if (unSupportedExtensions.indexOf(extension) != -1) {
                      alert(
                        "The file wasn't attached since its extension is not supported."
                      );
                      return;
                    }
                    var zsCurrentAttachmentIdTokens = jQuery(
                      zsAttachmentDetails
                    )
                      .attr("id")
                      .split("_");
                    var zsCurrentAttachmentId = parseInt(
                      zsCurrentAttachmentIdTokens[1]
                    );
                    var zsAttachmentIdToBeRemoved = jQuery.inArray(
                      zsCurrentAttachmentId,
                      zsAttachmentFileBrowserIdsList
                    );
                    zsAttachmentFileBrowserIdsList.splice(
                      zsAttachmentIdToBeRemoved,
                      1
                    );
                    var zsNextAttachmentId = zsAttachmentFileBrowserIdsList[0];
                    var zsnextAttachment = "zsattachment_" + zsNextAttachmentId;
                    jQuery("#zsattachment_" + zsCurrentAttachmentId).hide();
                    jQuery("#" + zsnextAttachment).show();
                    jQuery("#zsFileBrowseAttachments").append(
                      '<div class="filenamecls zsFontClass" id="file_' +
                        zsCurrentAttachmentId +
                        '">' +
                        ZSEncoder.encodeForHTML(zsFileName) +
                        '<a class="zsfilebrowseAttachment" style="margin-left:10px;" href="javascript:;" id="fileclose_' +
                        zsCurrentAttachmentId +
                        '">X</a></div>'
                    );
                    zsAttachedAttachmentsCount = zsAttachedAttachmentsCount + 1;
                  }
                }
                zsChangeMousePointer();
              }
              jQuery(document)
                .off("click.fba")
                .on("click.fba", ".zsfilebrowseAttachment", function () {
                  var currentlyDeletedElement = jQuery(this)
                    .attr("id")
                    .split("_")[1];
                  jQuery("#zsattachment_" + currentlyDeletedElement).val("");
                  jQuery(
                    "#zsattachment_" + currentlyDeletedElement
                  ).replaceWith(
                    jQuery("#zsattachment_" + currentlyDeletedElement).clone()
                  );
                  jQuery(this).parent().remove();
                  zsAttachedAttachmentsCount = zsAttachedAttachmentsCount - 1;
                  zsAttachmentFileBrowserIdsList.push(
                    parseInt(currentlyDeletedElement)
                  );
                  zsRearrangeFileBrowseAttachments();
                  zsChangeMousePointer();
                });
              function zsRearrangeFileBrowseAttachments() {
                jQuery.each(
                  jQuery("input[type = file]"),
                  function (fileIndex, fileObject) {
                    fileIndex = fileIndex + 1;
                    if (fileIndex == zsAttachmentFileBrowserIdsList[0]) {
                      jQuery("#zsattachment_" + fileIndex).show();
                    } else {
                      jQuery("#zsattachment_" + fileIndex).hide();
                    }
                  }
                );
              }
              function zsOpenFileBrowseAttachment(clickEvent) {
                if (zsAttachedAttachmentsCount >= 5) {
                  clickEvent.preventDefault();
                }
              }
              function zsChangeMousePointer() {
                if (zsAttachedAttachmentsCount >= 5) {
                  jQuery("#zsMaxLimitMessage").show();
                  jQuery(
                    "#zsattachment_1,#zsattachment_2,#zsattachment_3,#zsattachment_4,#zsattachment_5"
                  ).hide();
                  jQuery("#zsBrowseAttachment,#zsCloudAttachment").css(
                    "cursor",
                    "default"
                  );
                } else {
                  jQuery("#zsMaxLimitMessage").hide();
                  zsRearrangeFileBrowseAttachments();
                  jQuery("#zsBrowseAttachment,#zsCloudAttachment").css(
                    "cursor",
                    "pointer"
                  );
                }
              }
              function zsShowCaptcha() {
                jQuery("#zsCaptchaLoading").hide();
                jQuery("#zsCaptcha").show();
              }
              function zsRegenerateCaptcha() {
                var webFormxhr = {};
                webFormxhr = new XMLHttpRequest();
                webFormxhr.open(
                  "GET",
                  "https://desk.zoho.com/support/GenerateCaptcha?action=getNewCaptcha&_=" +
                    new Date().getTime(),
                  true
                );
                webFormxhr.onreadystatechange = function () {
                  if (
                    webFormxhr.readyState === 4 &&
                    webFormxhr.status === 200
                  ) {
                    try {
                      var response =
                        webFormxhr.responseText != null
                          ? JSON.parse(webFormxhr.responseText)
                          : "";
                      jQuery("#zsCaptchaUrl").load(zsShowCaptcha);
                      document.getElementById("zsCaptchaUrl").src =
                        response.captchaUrl;
                      document.getElementsByName("xJdfEaS")[0].value =
                        response.captchaDigest;
                    } catch (e) {}
                  }
                };
                webFormxhr.send();
              }
              document.addEventListener("readystatechange", function () {
                if (
                  document.readyState === "complete" &&
                  window.zsRegenerateCaptcha
                ) {
                  zsRegenerateCaptcha();
                }
                setAllDependancyFieldsMapping();
                document
                  .getElementById("zsSubmitButton_547474000041204025")
                  .removeAttribute("disabled");
                zsAttachedAttachmentsCount = 0;
                zsAttachmentFileBrowserIdsList = [1, 2, 3, 4, 5];
                document.forms["zsWebToCase_547474000041204025"][
                  "zsWebFormCaptchaWord"
                ].value = "";
                jQuery("#zsFileBrowseAttachments").html("");
                jQuery.each(
                  jQuery("input[type = file]"),
                  function (fileIndex, fileObject) {
                    var zsAttachmentId = jQuery(fileObject).attr("id");
                    var zsAttachmentNo = zsAttachmentId.split("_")[1];
                    var zsAttachedFile = jQuery(
                      "#zsattachment_" + zsAttachmentNo
                    );
                    if (
                      zsAttachedFile[0] != undefined &&
                      zsAttachedFile[0].files[0] != undefined
                    ) {
                      var zsFileBrowserAttachmentHtml = "";
                      zsAttachedFileName = zsAttachedFile[0].files[0].name;
                      zsFileBrowserAttachmentHtml =
                        '<div class="filenamecls zsFontClass" id="file_' +
                        zsAttachmentNo +
                        '">' +
                        ZSEncoder.encodeForHTML(zsAttachedFileName) +
                        '<a class="zsfilebrowseAttachment" style="margin-left:10px" href="javascript:;" id="fileclose_' +
                        zsAttachmentNo +
                        '">X</a></div>';
                      jQuery("#zsFileBrowseAttachments").append(
                        zsFileBrowserAttachmentHtml
                      );
                      zsAttachedAttachmentsCount =
                        zsAttachedAttachmentsCount + 1;
                      var zsAttachmentIdToBeRemoved = jQuery.inArray(
                        parseInt(zsAttachmentNo),
                        zsAttachmentFileBrowserIdsList
                      );
                      zsAttachmentFileBrowserIdsList.splice(
                        zsAttachmentIdToBeRemoved,
                        1
                      );
                    }
                  }
                );
                if (
                  document.forms["zsWebToCase_547474000041204025"][
                    "zsCloudPickerAttachments"
                  ] != undefined
                ) {
                  var zsCloudAttachments = jQuery.parseJSON(
                    document.forms["zsWebToCase_547474000041204025"][
                      "zsCloudPickerAttachments"
                    ].value
                  );
                  zsRenderCloudAttachments(zsCloudAttachments);
                }
                zsRearrangeFileBrowseAttachments();
                zsChangeMousePointer();
              });
              function zsResetWebForm(webFormId) {
                document.forms["zsWebToCase_" + webFormId].reset();
                document
                  .getElementById("zsSubmitButton_547474000041204025")
                  .removeAttribute("disabled");
                setAllDependancyFieldsMapping();
                zsAttachedAttachmentsCount = 0;
                zsAttachmentFileBrowserIdsList = [1, 2, 3, 4, 5];
                jQuery("#zsFileBrowseAttachments").html("");
                zsCloudPickerJSON = {};
                if (
                  document.forms["zsWebToCase_547474000041204025"][
                    "zsCloudPickerAttachments"
                  ] != undefined
                ) {
                  document.forms["zsWebToCase_547474000041204025"][
                    "zsCloudPickerAttachments"
                  ].value = JSON.stringify({});
                }
                zsRearrangeFileBrowseAttachments();
                zsChangeMousePointer();
              }
            </script>
          </head>
          <div class="arrow" style="margin-left: 35px">
            <button
              onclick="goToDeshboard()"
              style="
                color: white;
                background-color: #0076c1;
                border-radius: 2px;
                border-left: 2px solid #0076c1;
                border-right: 2px solid #0076c1;
                border-block: 2px solid #0076c1;
                width: 65px;
                font-size: 0.75rem;
                height: 25px;
                cursor: pointer;
              "
            >
              Back
            </button>
          </div>
          <div id="zohoSupportWebToCase" align="center">
            <form
              name="zsWebToCase_547474000041204025"
              id="zsWebToCase_547474000041204025"
              action="https://tickets.examroom.ai/support/WebToCase"
              method="POST"
              onSubmit="return zsValidateMandatoryFields()"
              enctype="multipart/form-data"
            >
              <input
                type="hidden"
                name="xnQsjsdp"
                value="edbsnbc421d47abd6727c9bb03195cca06104"
              />
              <input
                type="hidden"
                name="xmIwtLD"
                value="edbsn7cfddf5755adface8ac676438233573b20543c6fbb09f74a37a1b87043b03447"
              />
              <input type="hidden" name="xJdfEaS" value="" />
              <input type="hidden" name="actionType" value="Q2FzZXM=" />
              <input type="hidden" id="property(module)" value="Cases" />
              <input
                type="hidden"
                id="dependent_field_values_Cases"
                value='&#x7b;"JSON_VALUES"&#x3a;&#x7b;&#x7d;,"JSON_SELECT_VALUES"&#x3a;&#x7b;&#x7d;,"JSON_MAP_DEP_LABELS"&#x3a;&#x5b;&#x5d;&#x7d;'
              />
              <input
                type="hidden"
                name="returnURL"
                value="https&#x3a;&#x2f;&#x2f;credentiauat.examroom.ai&#x2f;support&#x2f;"
              />
              <table border="0" cellspacing="0" class="zsFormClass">
                <tr>
                  <td nowrap class="zsFontClass" width="25%" align="left">
                    Category &nbsp;&nbsp;
                  </td>
                  <td align="left" width="75%">
                    <select
                      name="Category"
                      value=""
                      onchange="setDependent(this, false)"
                      id="Category"
                    >
                      <option value="Applications">Applications</option>
                      <option value="Vouchers">Vouchers</option>
                      <option value="Email Update">Email Update</option>
                      <option value="Exam Detail">Exam Detail</option>
                      <option value="Exam Scheduling Issue">
                        Exam Scheduling Issue
                      </option>
                      <option value="Update Exam Status">
                        Update Exam Status
                      </option>
                      <option value="CNA365 System Issues">
                        CNA365 System Issues
                      </option>
                      <option value="Onboarding or Offboarding">
                        Onboarding or Offboarding
                      </option>
                      <option value="Activation">Activation</option>
                      <option value="Incorrect State or Disable Account">
                        Incorrect State or Disable Account
                      </option>
                      <option value="Reports">Reports</option>
                      <option value="Scores">Scores</option>
                      <option value="Training Program">Training Program</option>
                      <option value="Registry">Registry</option>
                      <option value="Exam Payments">Exam Payments</option>
                      <option value="Accommodations">Accommodations</option>
                      <option value="Grievance Request">
                        Grievance Request
                      </option>
                      <option value="Testing Applications">
                        Testing Applications
                      </option>
                      <option value="Letter of Good Standing/Verification">
                        Letter of Good Standing/Verification
                      </option>
                      <option value="Score Results (Ship and Score)">
                        Score Results (Ship and Score)
                      </option>
                      <option value="Score Results (Full Service States)">
                        Score Results (Full Service States)
                      </option>
                      <option value="PA Active Nurse Aide Registry List">
                        PA Active Nurse Aide Registry List
                      </option>
                      <option value="Facilities">Facilities</option>
                      <option value="Evaluator Support">
                        Evaluator Support
                      </option>
                      <option value="State Client Support">
                        State Client Support
                      </option>
                      <option value="Careers/Recruiting">
                        Careers/Recruiting
                      </option>
                      <option value="Training Instructors">
                        Training Instructors
                      </option>
                      <option value="Evaluator Skills Questions">
                        Evaluator Skills Questions
                      </option>
                    </select>
                  </td>
                </tr>
                <tr>
                  <td nowrap class="zsFontClass" width="25%" align="left">
                    Your First Name&nbsp;&nbsp;
                  </td>
                  <td align="left" width="75%">
                    <input
                      type="text"
                      maxlength="120"
                      name="First Name"
                      value=""
                    />
                  </td>
                </tr>
                <tr>
                  <td nowrap class="zsFontClass" width="25%" align="left">
                    Your Last Name&nbsp;&nbsp;
                  </td>
                  <td align="left" width="75%">
                    <input
                      type="text"
                      maxlength="120"
                      name="Contact Name"
                      class="manfieldbdr"
                    />
                  </td>
                </tr>
                <tr>
                  <td nowrap class="zsFontClass" width="25%" align="left">
                    Your Email&nbsp;&nbsp;
                  </td>
                  <td align="left" width="75%">
                    <input
                      type="text"
                      maxlength="120"
                      name="Email"
                      value=""
                      class="manfieldbdr"
                    />
                  </td>
                </tr>
                <tr>
                  <td nowrap class="zsFontClass" width="25%" align="left">
                    Subject&nbsp;&nbsp;
                  </td>
                  <td align="left" width="75%">
                    <input
                      type="text"
                      maxlength="255"
                      name="Subject"
                      value=""
                      class="manfieldbdr"
                    />
                  </td>
                </tr>
                <tr>
                  <td nowrap class="zsFontClass" width="25%" align="left">
                    Candidate ID Number&nbsp;&nbsp;
                  </td>
                  <td align="left" width="75%">
                    <input
                      type="text"
                      maxlength="120"
                      name="Candidate ID Number"
                      value=""
                      class="manfieldbdr"
                    />
                  </td>
                </tr>
                <tr>
                  <td nowrap class="zsFontClass" width="25%" align="left">
                    Priority &nbsp;&nbsp;
                  </td>
                  <td align="left" width="75%">
                    <select
                      name="Priority"
                      value=""
                      onchange="setDependent(this, false)"
                      id="Priority"
                    >
                      <option value="">-None-</option>
                      <option value="High">High</option>
                      <option value="Medium">Medium</option>
                      <option value="Low">Low</option>
                    </select>
                  </td>
                </tr>
                <tr>
                  <td nowrap class="zsFontClass" width="25%" align="left">
                    State &nbsp;&nbsp;
                  </td>
                  <td align="left" width="75%">
                    <input
                      type="text"
                      placeholder="Search.."
                      id="myInput"
                      onkeyup="filterFunction()"
                      autocomplete="off"
                    />
                    <div class="dropdown">
                      <div id="myDropdown" class="dropdown-content">
                        <label onclick="statSelect(this);"
                          ><input
                            type="checkbox"
                            value="Alabama"
                          />Alabama</label
                        >
                        <label onclick="statSelect(this);"
                          ><input type="checkbox" value="Alaska" />Alaska</label
                        >
                        <label onclick="statSelect(this);"
                          ><input
                            type="checkbox"
                            value="California"
                          />California</label
                        >
                        <label onclick="statSelect(this);"
                          ><input
                            type="checkbox"
                            value="Colorado"
                          />Colorado</label
                        >
                        <label onclick="statSelect(this);"
                          ><input
                            type="checkbox"
                            value="District of Columbia"
                          />District of Columbia</label
                        >
                        <label onclick="statSelect(this);"
                          ><input
                            type="checkbox"
                            value="Georgia"
                          />Georgia</label
                        >
                        <label onclick="statSelect(this);"
                          ><input
                            type="checkbox"
                            value="Maryland"
                          />Maryland</label
                        >
                        <label onclick="statSelect(this);"
                          ><input
                            type="checkbox"
                            value="Mississippi"
                          />Mississippi</label
                        >
                        <label onclick="statSelect(this);"
                          ><input type="checkbox" value="North Carolina" />North
                          Carolina</label
                        >
                        <label onclick="statSelect(this);"
                          ><input type="checkbox" value="Nevada" />Nevada</label
                        >
                        <label onclick="statSelect(this);"
                          ><input
                            type="checkbox"
                            value="Pennsylvania"
                          />Pennsylvania</label
                        >
                        <label onclick="statSelect(this);"
                          ><input type="checkbox" value="Rhode Island" />Rhode
                          Island</label
                        >
                        <label onclick="statSelect(this);"
                          ><input type="checkbox" value="South carolina" />South
                          carolina</label
                        >
                        <label onclick="statSelect(this);"
                          ><input
                            type="checkbox"
                            value="Virginia"
                          />Virginia</label
                        >
                        <label onclick="statSelect(this);"
                          ><input
                            type="checkbox"
                            value="Washington"
                          />Washington</label
                        >
                        <div style="clear: both"></div>
                      </div>
                      <div style="clear: both"></div>
                    </div>
                    <div style="clear: both"></div>
                  </td>
                </tr>
                <tr style="display: none">
                  <td nowrap class="zsFontClass" width="25%" align="left">
                    State &nbsp;&nbsp;
                  </td>
                  <td align="left" width="75%">
                    <select
                      name="State"
                      multiple
                      onchange="setDependent(this, false)"
                      id="CASECF41"
                      class="manfieldbdr"
                    >
                      <option value="Alabama">Alabama</option>
                      <option value="California">California</option>
                      <option value="Colorado">Colorado</option>
                      <option value="District of Columbia">
                        District of Columbia
                      </option>
                      <option value="Georgia">Georgia</option>
                      <option value="Maryland">Maryland</option>
                      <option value="Mississippi">Mississippi</option>
                      <option value="North Carolina">North Carolina</option>
                      <option value="Nevada">Nevada</option>
                      <option value="Pennsylvania">Pennsylvania</option>
                      <option value="Rhode Island">Rhode Island</option>
                      <option value="South carolina">South carolina</option>
                      <option value="Virginia">Virginia</option>
                      <option value="Washington">Washington</option>
                      <option value="Alaska">Alaska</option>
                    </select>
                  </td>
                </tr>
                <tr>
                  <td nowrap class="zsFontClass" width="25%" align="left">
                    Description &nbsp;&nbsp;
                  </td>
                  <td align="left" width="75%">
                    <textarea
                      name="Description"
                      maxlength="3000"
                      width="250"
                      height="250"
                    ></textarea>
                  </td>
                </tr>
                <tr>
                  <td nowrap class="zsFontClass" width="25%" align="left">
                    Attachment &nbsp;&nbsp;
                  </td>
                  <td align="center" width="75%">
                    <span
                      class="zsFontClass wtcuploadfile"
                      id="zsBrowseAttachment"
                      >Attach files</span
                    >
                    <input
                      class="wtcuploadinput"
                      type="file"
                      name="attachment_1"
                      id="zsattachment_1"
                      style="display: block"
                      onclick="zsOpenFileBrowseAttachment(event)"
                      onchange="zsRenderBrowseFileAttachment(this.value, this)"
                    /><input
                      class="wtcuploadinput"
                      type="file"
                      name="attachment_2"
                      id="zsattachment_2"
                      style="display: none"
                      onclick="zsOpenFileBrowseAttachment(event)"
                      onchange="zsRenderBrowseFileAttachment(this.value, this)"
                    />
                    <input
                      class="wtcuploadinput"
                      type="file"
                      name="attachment_3"
                      id="zsattachment_3"
                      style="display: none"
                      onclick="zsOpenFileBrowseAttachment(event)"
                      onchange="zsRenderBrowseFileAttachment(this.value, this)"
                    />
                    <input
                      class="wtcuploadinput"
                      type="file"
                      name="attachment_4"
                      id="zsattachment_4"
                      style="display: none"
                      onclick="zsOpenFileBrowseAttachment(event)"
                      onchange="zsRenderBrowseFileAttachment(this.value, this)"
                    />
                    <input
                      class="wtcuploadinput"
                      type="file"
                      name="attachment_5"
                      id="zsattachment_5"
                      onclick="zsOpenFileBrowseAttachment(event)"
                      onchange="zsRenderBrowseFileAttachment(this.value, this)"
                    />
                    <div class="clboth"></div>
                    <span
                      id="zsMaxSizeMessage"
                      style="color: black; font-size: 8px; float: left"
                      >Each of your file(s) can be up to 20MB in size.</span
                    ><span
                      id="zsMaxLimitMessage"
                      style="
                        color: black;
                        font-size: 8px;
                        float: left;
                        margin-left: 14px;
                        display: none;
                      "
                      >You can attach as many as 5 files at a time.</span
                    >
                    <div id="zsFileBrowseAttachments"></div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2" align="center">
                    <div id="zsCaptchaLoading">
                      <strong>Loading...<br /><br /></strong>
                    </div>
                    <div id="zsCaptcha" style="display: none">
                      <img src="#" id="zsCaptchaUrl" name="zsCaptchaImage" /><a
                        href="javascript:;"
                        style="
                          color: #00a3fe;
                          cursor: pointer;
                          margin-left: 10px;
                          vertical-align: middle;
                          text-decoration: none;
                        "
                        class="zsFontClass"
                        onclick="zsRegenerateCaptcha()"
                        >Refresh</a
                      >
                    </div>
                    <div>
                      <input type="text" name="zsWebFormCaptchaWord" />
                      <input type="hidden" name="zsCaptchaSrc" value="" />
                    </div>
                  </td>
                </tr>
                <tr>
                  <td
                    style="padding: 11px 5px 0px 46px"
                    colspan="2"
                    align="center"
                    width="25%"
                  >
                    <input
                      type="submit"
                      id="zsSubmitButton_547474000041204025"
                      class="zsFontButton"
                      value="Submit"
                    />
                    <input
                      type="button"
                      class="zsFontButton"
                      value="Reset"
                      onclick="zsResetWebForm('547474000041204025')"
                    />
                  </td>
                </tr>
              </table>
            </form>
          </div>
        </html>
      </div>
    </div>
    <!-- <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script> -->
    <script>
      /* When the user clicks on the button,
        toggle between hiding and showing the dropdown content */
      function myFunction() {
        document.getElementById("myDropdown").classList.toggle("show");
      }
      function statSelect(ths) {
        var chk = jQuery(ths).find("input").val();
        var par = jQuery(ths).parent();
        var dt = jQuery("#CASECF41 option").text();
        jQuery("#CASECF41 option").each(function () {
          var vl = jQuery(this).text();
          if (chk == vl) {
            if (jQuery(ths).find("input").is(":checked")) {
              jQuery(this).attr("selected", "selected");
            } else {
              jQuery(this).removeAttr("selected");
            }
          }
        });
      }
      function filterFunction() {
        var input, filter, ul, li, a, i;
        input = document.getElementById("myInput");
        filter = input.value.toUpperCase();
        div = document.getElementById("myDropdown");
        a = div.getElementsByTagName("label");
        for (i = 0; i < a.length; i++) {
          txtValue = a[i].textContent || a[i].innerText;
          if (txtValue.toUpperCase().indexOf(filter) > -1) {
            a[i].style.display = "";
          } else {
            a[i].style.display = "none";
          }
        }
      }
    </script>
    <script>
      console.log(zsWebReceiveMessage);
    </script>
  </body>
</html>
<script>
  function goToDeshboard() {
    // debugger;/
    window.location.href = "/login";
  }
  function validateFormWithCaptcha(event) {
    event.preventDefault();
    var recaptchaResponse = grecaptcha.getResponse();

    if (recaptchaResponse.length === 0) {
      alert("Please complete the reCAPTCHA.");
      return false;
    }

    // If reCAPTCHA is validated, proceed with form submission
    document.getElementById("zsWebToCase_547474000041204025").submit();
  }
</script>
