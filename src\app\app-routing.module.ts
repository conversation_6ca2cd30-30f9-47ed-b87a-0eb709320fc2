import { NgModule } from '@angular/core';
import { ExtraOptions, RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  // { path: '', component: CustomLayoutComponent },
  {
    path: '',
    loadChildren: () =>
      import('./authorization/authorization.module').then(
        (m) => m.AuthorizationModule
      ),
  },
  { path: '**', pathMatch: 'full', redirectTo: '' }
];

const config: ExtraOptions = {
  useHash: false
}

@NgModule({
  imports: [
    RouterModule.forRoot(routes, config),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule { }
