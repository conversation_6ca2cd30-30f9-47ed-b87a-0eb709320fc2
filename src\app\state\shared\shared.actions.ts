import { createAction, props } from "@ngrx/store";
// import { CartItem } from "../../scheduled/state/models/cartItem";
export const SET_LOADING_ACTION = "[shared state] set loading spinner";
export const SET_ERROR_MESSAGE = "[shared state] set error message";
export const SET_USER_DATA_TO_FROM_LOCAL_STORAGE =
  "[shared state] set user data to and from local storage";

export const setLoadingSpinner = createAction(
  SET_LOADING_ACTION,
  props<{ status: boolean }>()
);

export const setErrorMessage = createAction(
  SET_ERROR_MESSAGE,
  props<{ message: any }>()
);

// export const getCartItems = createAction(
//   "[CartItems] GET CartItems",
//   props<{ CartId?: number; personTenantRoleId?: number }>()
// );

// export const gotCartItems = createAction(
//   "[CartItems] GOT CartItems",
//   props<{ cartItems: CartItem[] }>()
// );

// export const setUserDetails = createAction(
//   "[UserDetails] setUserDetails",
//   props<{ token?: string; id?: string }>()
// );
