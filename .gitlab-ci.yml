#image: ubuntu:18.04
image: node:16.15.0
  #node:14.17.3
  #node:16.15.0

stages:
  - Build
  #- Test
  - Deploy

variables:
    GIT_STRATEGY: clone
    GIT_SUBMODULE_STRATEGY: normal
    GIT_SUBMODULE_UPDATE_FLAGS: --init --remote 
    #--merge --recursive

     #GIT_SUBMODULE_STRATEGY: normal

Build:
  stage: Build
  # tags: 
  #       - credentia
  script:
     
    #- git submodule sync
    #- git submodule init
    #- git submodule update
    #- npm i
    #- git submodule init
    #- git submodule update
    - npm cache clean --force
    - rm -rf node_modules
    - git submodule sync 
    #- git pull --recurse-submodules
    #- git submodule init
    - git submodule update --remote
    #- cd src/app/core/common-component/examroom-formbuilder
    #- ls
    - npm i
    #- npm i renovate@8.5.5
    #- npm install -g npm
    #- NODE_ENV=production node --max_old_space_size=6144
    - npm install -g @angular/cli@11.1.0
    - npm run build 
    #- node --version
    #- npm install nx -g
    #- ng build --c qa --base-href /login/
    #- nx run-many --parallel --target=build --all --maxParallel=3 --prod
  artifacts:
    expire_in: 1 week
    paths:
      - dist

  only:
    - UAT    
# Sonarqube:
#   stage: Test
#   trigger:
#     include:
#       - s.gitlab-ci.yml
#   only:
#     - QA

deploy-S3:
  image: node:16.15.0
  stage: Deploy
  # tags: 
  #       - credentia
  before_script:
    
    - node --version
    - npm install -g @angular/cli@latest #change_Me
    #- ng new ibui --routing #change_Me
    - apt-get update
    - apt-get install awscli -y
    - aws configure set aws_access_key_id ********************; aws configure set aws_secret_access_key VxaSL8t7G+sZ6n6Hq9UY+BgdRrXv3/wqUNnL3ufD; aws configure set default.region us-east-2


  script:
    - aws --version
    - ls
    - cp -r dist/exai/* dist
    - rm -r dist/exai
    - aws s3 rm s3://credentia-uat-login --recursive
    - aws s3 cp ./dist s3://credentia-uat-login --recursive
    - echo "Invalidate index.html"
    - aws cloudfront create-invalidation --distribution-id E29HLMM1835DMY --paths /index.html
  environment:
    name: uat
  only:
    - UAT 



