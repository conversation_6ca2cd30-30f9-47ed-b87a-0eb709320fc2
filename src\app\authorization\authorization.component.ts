import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { getLoading } from '../state/shared/shared.selectors';

@Component({
  selector: 'exai-authorization',
  templateUrl: './authorization.component.html',
  styleUrls: ['./authorization.component.scss']
})
export class AuthorizationComponent implements OnInit {

  constructor(private store : Store) { }

  loadingObs: Observable<boolean>;

  ngOnInit(): void {
    this.loadingObs = this.store.select(getLoading);
  }

}
