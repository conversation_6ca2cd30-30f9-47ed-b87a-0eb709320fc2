import { Injectable } from "@angular/core";
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpResponse,
  HttpErrorResponse,
} from "@angular/common/http";
import { Observable, of } from "rxjs";
import { catchError, map, tap } from "rxjs/operators";
import { Action, Store } from "@ngrx/store";
import {
  setErrorMessage,
  setLoadingSpinner,
} from "./state/shared/shared.actions";
@Injectable({
  providedIn: "root",
})
export class BufferInterceptor implements HttpInterceptor {
  constructor(private store: Store) { }
  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    this.store.dispatch<Action>(setLoadingSpinner({ status: true }));
    return next.handle(request).pipe(
      tap((event) => {
        if (event instanceof HttpResponse) {
          this.store.dispatch<Action>(setLoadingSpinner({ status: false }));
        }
      }, (error: HttpErrorResponse) => {
        this.store.dispatch<Action>(setErrorMessage({ message: error.error }));
        this.store.dispatch<Action>(setLoadingSpinner({ status: false }));
      })
    );
  }
}
