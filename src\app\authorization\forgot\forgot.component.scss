.bg-pattern {
    background: var(--page_background);
}
.card {
    border: 0.3px solid var(--card-border);
    border-radius: 4px;
}

.loginFormLogo {
    padding: 2.75rem;
    padding-bottom: 0.5rem;
}

h3 {
    color: var(--forgot-password-heading);
}

h4 {
    color: var(--forgot-password-heading);
}

.loginForm {
    padding: 0 2.75rem 2.75rem;
}

.loginBtn {
    background-color: var(--primary);
    font-size: 12px;
    color: var(--white);
    &:hover:enabled {
        background-color: var(--btn-hover);
    }
    &:active:enabled {
        background-color: var(--btn-pressed);
    }
}

a {
    cursor: pointer;
    color: var(--primary);
    text-decoration: none;
    &:hover {
        color: var(--btn-hover);
    }
    &:active {
        color: var(--btn-pressed);
    }
}

.leftArrow {
    font-size: 20px;
}
.dialog{
    // padding-top: 60px;
}

// loader
.card-div {
    height: 5rem;
    width: 5rem;
    position: absolute;
    margin-left: 45%;
  }
  .spinner {
    margin: 20px auto 0;
    width: 70px;
    text-align: center;
  }
  .spinner > div {
    width: 18px;
    height: 18px;
    background-color: var(--primary);
    border-radius: 100%;
    display: inline-block;
    -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
    animation: sk-bouncedelay 1.4s infinite ease-in-out both;
  }
  .spinner .bounce1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
  }
  .spinner .bounce2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
  }
  @-webkit-keyframes sk-bouncedelay {
    0%,
    80%,
    100% {
      -webkit-transform: scale(0);
    }
    40% {
      -webkit-transform: scale(1);
    }
  }
  @keyframes sk-bouncedelay {
    0%,
    80%,
    100% {
      -webkit-transform: scale(0);
      transform: scale(0);
    }
    40% {
      -webkit-transform: scale(1);
      transform: scale(1);
    }
  }
  .ButtonChat{
    color: #1c75bc;
    // background-color: #1c75bc;
  }
  .chat{
    display: flex;
    flex-direction: row-reverse;
    padding-right: 2%;
    padding-top: 1%;
    background-color: #f9f9f9;
  }
  // .mat-icon-button{
   
  //   line-height: 2px;
  
  // }
  .flagLogo{
    flex-direction: row !important;
    align-items: initial;
  }
  .w-5{
    width: 1.25rem;
    padding-bottom: 11px;
  }
  
