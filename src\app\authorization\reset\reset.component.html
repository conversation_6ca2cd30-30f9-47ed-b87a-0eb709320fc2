<ng-container *ngIf="loadingObs | async">
    <div class="card-div" fxLayout="column" fxLayoutAlign="center center">
        <div class="spinner w-full">
            <div class="bounce1"></div>
            <div class="bounce2"></div>
            <div class="bounce3"></div>
        </div>
    </div>
</ng-container>
<div class="w-full h-full bg-pattern" fxLayout="column" fxLayoutAlign="center center">

    <div class="toolbar w-full px-gutter py-3 formsHeader" fxLayout="row" fxLayoutAlign="space-between center"
        exaiContainer>
        <a class="ltr:mr-4 rtl:ml-4 block" fxLayout="column" fxLayoutAlign="start start">
            <img class="w-48 select-none" src="assets/img/demo/logo.svg">
        </a>
        <div class="ltr:mr-4 rtl:ml-4 block flagLogo" fxLayout="column" fxLayoutAlign="end end">
            <img class="w-5 select-none" src="assets/img/demo/US.svg">
        </div>
    </div>

    <div class="card overflow-hidden w-full loginFormWidth max-w-xs">

        <div style="margin-top: 30px;" *ngIf="!disableAccessCode">
            <p class="note text-center t-xs" style="color: red;"> <b> Note: </b>Please update the password as per new
                policy.</p>
        </div>




        <div class="loginFormLogo" fxLayout="column" fxLayoutAlign="center center">
            <div class="fill-current text-center">
                <h3>{{global.Reset_Password}}</h3>
            </div>
        </div>


        <div [formGroup]="form" class="loginForm" fxLayout="column">
            <div fxFlex="auto" fxLayout="column" class="loginInput registerInput">

                <mat-form-field fxFlex="grow" class="example-full-width" appearance="outline" class="eyeIcon">
                    <mat-label>{{global.New_Password}}</mat-label>
                    <input (keyup)="paaEventCap($event)" type="password" (input)="paaEventCap1($event)" matInput
                        placeholder="{{global.New_Password}}" formControlName="password"
                        [type]="hide ? 'password' : 'text'"
                        [ngClass]="{'is-invalid': (!password.valid && password.touched), 'is-valid': (password.valid && password.touched)}" />
                    <mat-icon mat-icon-button matSuffix (click)="togglePwd()" [attr.aria-label]="'Hide password'"
                        [attr.aria-pressed]="hide">{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
                    <mat-error *ngFor="let validation of validation_messages.password">
                        <mat-error class="error-message"
                            *ngIf="form.get('password').hasError(validation.type) && (form.get('password').dirty || form.get('password').touched)">
                            {{validation.message}}</mat-error>
                    </mat-error>

                </mat-form-field>
                <div class="error_font_size  font-medium mt-1 ml-2 w-48" *ngIf=" form.value.password != '' ">
                    {{checkValueTrue()}}
                </div>

                <mat-form-field fxFlex="grow" class="example-full-width mt-2 margin_top_value" appearance="outline"
                    class="eyeIcon">
                    <mat-label>{{global.Confirm_Password}}</mat-label>
                    <input type="password" matInput placeholder="{{global.Confirm_Password}}"
                        formControlName="confirmPassword" [type]="hide ? 'password' : 'text'" [ngClass]="{
                      'is-invalid': (!form.get('confirmPassword').valid && form.get('confirmPassword').touched),
                      'is-valid': (form.get('confirmPassword').valid && form.get('confirmPassword').touched)
                    }" />
                    <mat-icon mat-icon-button matSuffix (click)="togglePwd()" [attr.aria-label]="'Hide password'"
                        [attr.aria-pressed]="hide">{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
                    <mat-error class="error-message"
                        *ngIf="form.get('confirmPassword').touched && !form.get('confirmPassword').valid">
                        {{global.Passwords_not_matched}}
                    </mat-error>
                </mat-form-field>
            </div>

            <button mat-raised-button class="loginBtn mb-2 submitPwd" (click)="resetPwd()"
                (keydown)="keyDownFunction($event)"
                [disabled]="!checkStatus() || resetPwdBtnClicked || paaswordProductName==true || passwordContainsFullNamePartnew==true">
                {{global.Reset_Password_btn}}</button>
            <button mat-raised-button class="loginBtn" (click)="forgotPwd()">{{global.Back}}</button>
        </div>




    </div>
    <exai-footer class="exai-footer" style="position: absolute;"></exai-footer>

</div>