<ng-container *ngIf="loadingObs | async">
    <div class="card-div" fxLayout="column" fxLayoutAlign="center center">
        <div class="spinner w-full">
            <div class="bounce1"></div>
            <div class="bounce2"></div>
            <div class="bounce3"></div>
        </div>
    </div>
</ng-container>
<div class="w-full h-full bg-pattern dialog" fxLayout="column" fxLayoutAlign="center center">
    <div class="toolbar w-full px-gutter py-3 formsHeader" fxLayout="row" fxLayoutAlign="space-between center"
        exaiContainer>
        <a class="ltr:mr-4 rtl:ml-4 block" fxLayout="column" fxLayoutAlign="start start">
            <img class="w-48 select-none" src="assets/img/demo/logo.svg">
        </a>
        <div class="ltr:mr-4 rtl:ml-4 block flagLogo" fxLayout="column" fxLayoutAlign="end end">
            <img class="w-5 select-none" src="assets/img/demo/US.svg">
        </div>
    </div>
    <div class="card overflow-hidden w-full loginFormWidth max-w-xs">
        <div class="loginFormLogo" fxLayout="column" fxLayoutAlign="center center">
            <div class="fill-current text-center">
                <h3>{{global.Two_Factor_Authentication_Required}}</h3>
            </div>
        </div>
        <div class="text-center pb-6">
            <h4 class="body-2 text-secondary m-0">
                {{global.Please_select_your_preferred_two_factor_authentication_method}}</h4>
        </div>

        <div [formGroup]="form" class="loginForm pb-3" fxLayout="column">
            <div fxFlex="auto" fxLayout="column" class="loginInput">
                <mat-radio-group  color="primary" aria-label="Select an option" style="text-align: center; margin-bottom: 1rem;"
                    class="example-full-width" matInput formControlName="preferredMFA">
                    <mat-radio-button value="1" style="margin-right:.8rem">{{global.email}}</mat-radio-button>
                    <mat-radio-button value="2">{{global.Text_Message}}</mat-radio-button>
                </mat-radio-group>
                <mat-error *ngFor="let validation of validation_messages.preferredMFA">
                    <mat-error class="error-message"
                        *ngIf="form.get('preferredMFA').hasError(validation.type) && (form.get('preferredMFA').dirty || form.get('preferredMFA').touched)">
                        {{validation.message}}</mat-error>
                </mat-error>
            </div>

            <button class="loginBtn" mat-raised-button type="button" (click)="selectPreferredMFA();"
                [disabled]="!form.valid || submitBtnClicked">
                {{global.Submit}}
            </button>

            <div fxLayout="row" fxLayoutAlign="center center">
                <a [routerLink]="['']" class="pt-4 m-px" fxLayout="row" fxLayoutAlign="center center">
                    <mat-icon class="leftArrow">keyboard_arrow_left</mat-icon> {{global.Back_to_Sign_In}}
                </a>
            </div>
        </div>
    </div>
    <exai-footer class="exai-footer" style="position: absolute;"></exai-footer>
</div>