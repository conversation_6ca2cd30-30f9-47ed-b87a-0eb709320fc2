.grid {
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    width: 100%; // Ensure full width

    >.grid-auto {
        &:first-child {
            margin-right: 1.5rem;
            display: flex;
            min-height: 36px;
            min-width: 36px;
        }

        &:nth-child(2) {
            flex: 1; // Make the message take up available space
            word-break: break-word; // Allow long words to break

            p {
                margin: 0;
                padding: 8px 0;
            }
        }

        &:last-child {
            margin-left: 0.5rem;
            display: flex;
        }
    }
}