{"name": "examroom.ai", "version": "11.1.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "NODE_ENV=production node --max_old_space_size=6144 ./node_modules/@angular/cli/bin/ng build --configuration production", "build:prod": "ng build --prod --base-href /login/", "build:uat": "ng build --c uat --base-href /login/", "build:qa": "ng build --c qa --base-href /login/", "build:dev": "ng build --c dev --base-href /login/", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "postinstall": "ngcc --properties es2015 browser module main --first-only --create-ivy-entry-points --tsconfig './tsconfig.app.json'", "bundle-report": "webpack-bundle-analyzer dist/exai/stats.json"}, "private": true, "dependencies": {"@angular/animations": "~12.1.0", "@angular/cdk": "~11.2.4", "@angular/common": "~12.1.0", "@angular/compiler": "~12.1.0", "@angular/core": "~12.1.0", "@angular/flex-layout": "11.0.0-beta.33", "@angular/forms": "~12.1.0", "@angular/material": "~11.2.4", "@angular/platform-browser": "~12.1.0", "@angular/platform-browser-dynamic": "~12.1.0", "@angular/router": "~12.1.0", "@iconify/icons-emojione": "~1.1.0", "@iconify/icons-fa-brands": "~1.1.0", "@iconify/icons-fa-solid": "~1.1.0", "@iconify/icons-ic": "~1.1.3", "@iconify/icons-logos": "~1.1.9", "@ngneat/until-destroy": "~8.0.4", "@ngrx/effects": "^12.5.1", "@ngrx/store": "^12.5.1", "@ngx-loading-bar/core": "~5.1.1", "@ngx-loading-bar/router": "~5.1.1", "@visurel/iconify-angular": "~11.0.0", "apexcharts": "^3.27.2", "date-fns": "~2.19.0", "highlight.js": "~10.6.0", "jwt-decode": "^3.1.2", "libphonenumber-js": "^1.9.38", "luxon": "~1.26.0", "ng-apexcharts": "^1.5.12", "ng2-charts": "^2.4.2", "ng2-tooltip-directive": "^2.10.3", "ngx-mat-intl-tel-input": "^4.1.0", "rxjs": "~6.6.0", "simplebar": "~5.3.0", "tailwindcss": "~2.0.3", "tslib": "^2.0.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~12.1.0", "@angular/cli": "~12.1.0", "@angular/compiler-cli": "~12.1.0", "@angular/language-service": "~12.1.0", "@types/faker": "^5.1.5", "@types/luxon": "~1.25.0", "@types/node": "~14.14.7", "@types/showdown": "~1.9.3", "@types/simplebar": "~5.1.1", "codelyzer": "~6.0.1", "postcss": "^8.1.0", "postcss-scss": "^3.0.5", "typescript": "~4.3.4", "webpack-bundle-analyzer": "^4.4.2"}}