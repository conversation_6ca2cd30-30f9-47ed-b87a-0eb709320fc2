import {Component, OnInit, HostListener} from '@angular/core';
import {AbstractControl, FormBuilder, FormGroup, Validators} from '@angular/forms';
import {HttpService} from 'src/app/http.service';
import {Router} from "@angular/router";
import {GlobalService} from 'src/app/global.service';
import {AuthorizationService} from '../authorization.service';
import {Observable} from 'rxjs';
import {getLoading} from 'src/app/state/shared/shared.selectors';
import {Store} from '@ngrx/store';

@Component({
    selector: 'exai-mfa-selector',
    templateUrl: './mfa-selector.component.html',
    styleUrls: ['./mfa-selector.component.scss']
})
export class MfaSelectorComponent implements OnInit {

    form: FormGroup;
    preferredMFA: AbstractControl;
    btnEnabled: any;
    submitBtnClicked: boolean = false;

    constructor(public global: GlobalService,
                private fb: FormBuilder,
                private services: HttpService,
                private _router: Router,
                private _authorizationService: AuthorizationService,
                private _store: Store) {
        const info = _authorizationService.tempPasswordInfo;

        if (!(info && 'email' in info && 'password' in info)) {
            this._router.navigateByUrl('');
        }
    
        this.email = info.email;
        this.password = info.password;
    }

    email = '';
    password = '';

    validation_messages = {
        'preferredMFA': [
            {type: 'required', message: this.global.Code_is_required}
        ]
    };

    loadingObs: Observable<boolean>;

    ngOnInit() {

        this.loadingObs = this._store.select(getLoading)

        this.form = this.fb.group({
            preferredMFA: ['', Validators.compose([Validators.required])],
        });

        this.preferredMFA = this.form.controls['preferredMFA'];
    }

    selectPreferredMFA(): void {
        this.submitBtnClicked = true;
        const inputDetails = {
            username: this.email,
            password: this.password,
            preferredMFAMethod: parseInt(this.form.value.preferredMFA)
        };
        this.services.customLogin(inputDetails).subscribe((data: any) => {
            if (data) {
                this._authorizationService.tempPasswordInfo.session = data.session;
                this._router.navigateByUrl('confirmMFA')
            } else {
                this.services.callSnackbaronError("error");
                this.submitBtnClicked = false;
            }
        }, err => {
            this.services.callSnackbaronError(err.error);
            this.submitBtnClicked = false;
        });
    }
}
