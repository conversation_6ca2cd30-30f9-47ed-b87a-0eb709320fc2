import { Component, OnInit } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { GlobalService } from 'src/app/global.service';

@Component({
  selector: 'exai-support',
  templateUrl: './support.component.html',
  styleUrls: ['./support.component.scss']
})
export class SupportComponent implements OnInit {
  userDetails: GlobalService;

  constructor(private dialogRef: MatDialogRef<SupportComponent>) { 
  }

  ngOnInit(): void {
  }
  close() {
    this.dialogRef.close();
  }

}
