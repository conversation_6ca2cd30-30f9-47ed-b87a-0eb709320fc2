import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../shared-module.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RegisterModule } from './register/register.module';
import { AuthorizationRoutingModule } from './authorization-routing.module';
import { FooterModule } from 'src/@exai/layout/footer/footer.module';
import { AuthorizationComponent } from './authorization.component';
import { LoginComponent } from './login/login.component';
import { ForgotComponent } from './forgot/forgot.component';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ContainerModule } from 'src/@exai/directives/container/container.module';
import { PageLayoutModule } from 'src/@exai/components/page-layout/page-layout.module';
import { ResetComponent } from './reset/reset.component';
import { SnackbarComponent } from './snackbar/snackbar.component';
import { ChangepasswordComponent } from './changepassword/changepassword.component';
import { ConfirmMfaComponent } from './confirm-mfa/confirm-mfa.component';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { BufferInterceptor } from '../buffer-service.service';
import { Store, StoreModule } from '@ngrx/store';
import { SHARED_STATE_NAME } from '../state/shared/shared.selectors';
import { SharedReducer } from '../state/shared/shared.reducers';
import {TooltipModule} from 'ng2-tooltip-directive';
import { MfaSelectorComponent } from './mfa-selector/mfa-selector.component';
import {MatTooltipModule} from '@angular/material/tooltip';
import { MatCheckboxModule } from '@angular/material/checkbox';

@NgModule({
  declarations: [
    AuthorizationComponent,
    LoginComponent,
    ForgotComponent,
    ResetComponent,
    SnackbarComponent,
    ChangepasswordComponent,
    ConfirmMfaComponent,
    MfaSelectorComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    FormsModule,
    ReactiveFormsModule,
    FlexLayoutModule,
    ContainerModule,
    AuthorizationRoutingModule,
    FooterModule,
    PageLayoutModule,
    RegisterModule,
    MatTooltipModule,
    StoreModule.forFeature(SHARED_STATE_NAME,SharedReducer ),
    MatCheckboxModule
  ],
  providers:[
    { provide: HTTP_INTERCEPTORS, useClass: BufferInterceptor, multi: true },
    Store,
    TooltipModule
  ]
})
export class AuthorizationModule { }
