import { setLoadingSpinner, setErrorMessage } from './shared.actions';
import { createReducer, on } from '@ngrx/store';
import { initialState } from './shared.state';
// import { userDetails } from "../../candiate.types";
import { environment } from 'src/environments/environment';
import jwt_decode from 'jwt-decode';

const _sharedReducer = createReducer(
  initialState,
  on(setLoadingSpinner, (state, action) => {
    return {
      ...state,
      showLoading: action.status,
    };
  }),
  on(setErrorMessage, (state, action) => {
    return {
      ...state,
      errorMessage: action.message,
    };
  })
  //   on(setUserDetails, (state, action) => {
  //     var userData: userDetails = new userDetails();

  // if (sessionStorage.getItem("userData")) {
  //   userData = JSON.parse(sessionStorage.getItem("userData"));
  // } else if (action.token && action.id) {
  //   debugger;
  //   userData.roles = [{ personTenantRoleId: Number(action.id) }];
  //   userData.identityToken = action.token;
  //   userData.decodedIdentityToken = jwt_decode(action.token);
  //   sessionStorage.setItem("userData", JSON.stringify(userData));
  // } else {
  //   // window.location.href = environment.redirectUrl;
  // }

  //     return {
  //       ...state,
  //       userdetails: userData,
  //     };
  //   }),
  //   on(gotCartItems, (state, action) => {
  //     return {
  //       ...state,
  //       cartItems: action.cartItems,
  //     };
  //   })
);

export function SharedReducer(state, action) {
  return _sharedReducer(state, action);
}
