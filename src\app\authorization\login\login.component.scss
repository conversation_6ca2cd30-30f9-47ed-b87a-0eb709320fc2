.bg-pattern {
    background: var(--page_background);
}

.card {
    border: 0.3px solid var(--card-border);
    border-radius: 4px;
    margin-bottom: 4%;
}

.loginFormLogo {
    padding: 2.75rem;
    padding-bottom: 2.2rem;
}

.loginForm {
    padding: 0 2.75rem 2.75rem;
}

.loginBtn {
    background-color: var(--primary);
    font-size: 12px;
    color: var(--white);
    &:hover:enabled {
        background-color: var(--btn-hover);
    }
    &:active:enabled {
        background-color: var(--btn-pressed);
    }
}

.more {
    padding: 1.8em 0 1.7em;
    line-height: 20px;
    display: block;
    position: relative;
    &:before {
        border-top: solid 1px var(--card-border);
        content: "";
        height: 1px;
        width: 35%;
        display: table-cell;
    }
    &:after {
        border-top: solid 1px var(--card-border);
        content: "";
        height: 1px;
        width: 35%;
        display: table-cell;
    }
    > span {
        display: table-cell;
        width: 30%;
        white-space: nowrap;
        padding: 0 24px;
        color: var(--card-border);
        font-size: 14px;
        > span {
            margin-top: -10px;
            display: block;
            font-size: 14px;
        }
    }
}

.more-btn {
    display: flex;
    justify-content: space-around;
    > img {
        cursor: pointer;
        height: 40px;
        width: auto;
    }
}

.haveAccount {
    // padding-top: 2.8em;
    padding-top: 1.8em;
    font-size: 14px;
    color: var(--card-border);
}

a {
    cursor: pointer;
    color: var(--primary);
    text-decoration: none;
    &:hover {
        color: var(--btn-hover);
    }
    &:active {
        color: var(--btn-pressed);
    }
}

// loader
.card-div {
    height: 5rem;
    width: 5rem;
    position: absolute;
    margin-left: 45%;
  }
  .spinner {
    margin: 20px auto 0;
    width: 70px;
    text-align: center;
  }
  .spinner > div {
    width: 18px;
    height: 18px;
    background-color: var(--primary);
    border-radius: 100%;
    display: inline-block;
    -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
    animation: sk-bouncedelay 1.4s infinite ease-in-out both;
  }
  .spinner .bounce1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
  }
  .spinner .bounce2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
  }
  @-webkit-keyframes sk-bouncedelay {
    0%,
    80%,
    100% {
      -webkit-transform: scale(0);
    }
    40% {
      -webkit-transform: scale(1);
    }
  }
  @keyframes sk-bouncedelay {
    0%,
    80%,
    100% {
      -webkit-transform: scale(0);
      transform: scale(0);
    }
    40% {
      -webkit-transform: scale(1);
      transform: scale(1);
    }
  }
  .ButtonChat{
    color: #1c75bc;
    // background-color: #1c75bc;
  }
  .chat{
    display: flex;
    flex-direction: row-reverse;
    padding-right: 2%;
    padding-top: 1%;
    background-color: #f9f9f9;
  }
  .mat-icon-button{
   
    line-height: 2px;
  
  }
  
  .role-banner {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    margin-top: 4px;
  }
  
  .role-banner .line {
    flex: 1;
    height: 1px;
    background-color: #ccc;
    opacity: 0.5;
  }
  
  .role-banner .text {
    margin: 0 12px;
    padding: 6px 12px;
    font-weight: bold;
    border-radius: 4px;
    white-space: nowrap;
  }
  

