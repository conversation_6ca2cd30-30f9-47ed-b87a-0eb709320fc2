Stack trace:
Frame         Function      Args
0007FFFF9AA0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF89A0) msys-2.0.dll+0x2118E
0007FFFF9AA0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF9AA0  0002100469F2 (00021028DF99, 0007FFFF9958, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9AA0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9AA0  00021006A545 (0007FFFF9AB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9AB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9A67C0000 ntdll.dll
7FF9A51F0000 KERNEL32.DLL
7FF9A3EA0000 KERNELBASE.dll
7FF99FC50000 apphelp.dll
7FF9A5600000 USER32.dll
7FF9A3E70000 win32u.dll
000210040000 msys-2.0.dll
7FF9A6410000 GDI32.dll
7FF9A3A80000 gdi32full.dll
7FF9A3910000 msvcp_win.dll
7FF9A4410000 ucrtbase.dll
7FF9A4760000 advapi32.dll
7FF9A4F70000 msvcrt.dll
7FF9A5AB0000 sechost.dll
7FF9A52C0000 RPCRT4.dll
7FF9A2F40000 CRYPTBASE.DLL
7FF9A3DD0000 bcryptPrimitives.dll
7FF9A5970000 IMM32.DLL
