.submit {
    color: #0076C1;
}
.confirm {
    color: var(--text-popup);
}
.cardBrd {
    border: var(--save-draft-border);
    border-radius: var(--border-radius);
}

.dialog-close {
    justify-content: end!important;
    padding-right: 0.5rem!important;
}

.icon-1 {
    margin-right: 1rem!important;
}

.icon-2 {
    display: flex;
    padding-top: 0.2rem;
    font-size: 1.1rem;
    margin-right: 0.25rem;
}

.mat-icon {
    height: 18px;
    width: 18px;
}

.container-1 {
    background-color: #7d7d7d11;
}