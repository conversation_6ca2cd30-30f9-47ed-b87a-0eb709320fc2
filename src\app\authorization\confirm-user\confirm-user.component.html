<!-- <div class="w-full h-full bg-pattern" fxLayout="column" fxLayoutAlign="center center">

    <div class="toolbar w-full px-gutter py-3 formsHeader" fxLayout="row" fxLayoutAlign="space-between center"
        exaiContainer>
        <a class="ltr:mr-4 rtl:ml-4 block" fxLayout="column" fxLayoutAlign="start start">
            <img class="w-48 select-none" src="assets/img/demo/logo.svg">
        </a>
        <div class="ltr:mr-4 rtl:ml-4 block flagLogo" fxLayout="column" fxLayoutAlign="end end">
            <img class="w-5 select-none" src="assets/img/demo/US.svg">
        </div>
    </div>

    <div class="card overflow-hidden w-full verifyEmailWidth max-w-xs px-8 py-4">
        <div class="p-6 pb-0 loginFormLogo" fxLayout="column" fxLayoutAlign="center center">
            <div class="fill-current text-center">
                <h3>{{global.Your_email_is_Verified}}</h3>
            </div>
        </div>
        <div class="text-center"> -->
            <!-- <p class="title-p">{{global.Your_email_id_is_successfully_verified}}</p> -->
            <!-- <p class="title-p mb-8">{{global.Now_you_can_Login}}</p>
        </div>
        <img class="mb-6 emailImg mx-auto" src="assets/img/demo/Asset11.svg">
        <div class="p-6 text-center">
            <button mat-stroked-button class="strokedBtn float-end mb-2"
                [routerLink]="''">{{global.Continue_to_Login}}</button>
        </div>
    </div>
</div>

<exai-footer class="exai-footer" style="position: absolute;"></exai-footer> -->