import { Component, HostListener, OnInit } from '@angular/core';
import { GlobalService } from 'src/app/global.service';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpService } from 'src/app/http.service';
import { Router } from '@angular/router';
import { AuthorizationService } from '../authorization.service';
import { Observable } from 'rxjs';
import { getLoading } from 'src/app/state/shared/shared.selectors';
import { Store } from '@ngrx/store';
@Component({
  selector: 'exai-changepassword',
  templateUrl: './changepassword.component.html',
  styleUrls: ['./changepassword.component.scss']
})
export class ChangepasswordComponent implements OnInit {
  CheckValueNameorProduct:string
  form: FormGroup;
  email: AbstractControl;
  password: AbstractControl;
  oldpwd: AbstractControl;
  btnEnabled: any;
  resetPasswordTest:any;
  firstName;
  middleName;
  lastName;
  passwordContainsFullNamePartnew;
  paaswordProductName;
  submitBtnClicked: boolean = false;
  namePartRegex:any;
  constructor(public global: GlobalService, private fb: FormBuilder,
    private services: HttpService, private router: Router, private _authorizationService: AuthorizationService, private store: Store) {
    const info = _authorizationService.tempPasswordInfo;
    info && 'email' in info && 'tempPwd' in info ? "" : this.router.navigateByUrl('');
  }

  loadingObs: Observable<boolean>;

  validation_messages = {
    'oldpwd': [{ type: 'required', message: this.global.Password_is_required }],
    'email': [
      { type: 'required', message: this.global.Email_is_required },
      { type: 'pattern', message: this.global.Enter_a_valid_email }
    ],
    'password': [
      { type: 'required', message: this.global.Password_is_required },
      { type: 'pattern', message: this.global.Use_12_or_more_characters },
      { type: 'maxlength', message: this.global.Password_should_not_exceed_99_characters }
    ],
  };

  ngOnInit(): void {
    this.loadingObs = this.store.select(getLoading)

    const info = this._authorizationService.tempPasswordInfo;
    const emailforNameSearch=this._authorizationService.tempPasswordInfo.email;
    this.services.getCandidateName(emailforNameSearch).subscribe((data:any)=>{
      this.firstName=data.firstName;
      this.lastName=data.lastName;
      this.middleName=data.middleName;
     })

    this.form = this.fb.group({
      oldpwd: [info && 'tempPwd' in info ? info.tempPwd : "", Validators.required],
      email: [info && 'email' in info ? info.email : "", Validators.compose([Validators.required,
      Validators.pattern('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,10}$')])],
      password: ['', [Validators.required,
        Validators.minLength(12),
        Validators.maxLength(99),
        Validators.pattern('^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{12,}$'),
        ],],
      confirmPassword: ['', []]
    },
      {
        validator: [confirmPassword('password', 'confirmPassword')]
      });
    this.oldpwd = this.form.controls['oldpwd'];
    this.email = this.form.controls['email'];
    this.password = this.form.controls['password'];
  }

  // To check equality of password and confirm password
  checkStatus(): boolean {
    const controls = this.form.controls;
    const invalid = []
    for (const name in controls) {
      invalid.push({ name: name, valid: !controls[name].invalid, })
    }
    const status = invalid.every((ele: any) => ele.valid == true);
    return status;
  }
  passwordPolicy($event){
    this.global.passEv($event);
    this.paaswordProductName=this.global.paaswordProductName; 
  }

  passwordNamePolicy($event){
    this.global.firstNameevent=this.firstName;
    this.global.lastNameevent=this.lastName;
    this.global.middleNameevent=this.middleName;
    this.global.passTextChecking($event);
    this.passwordContainsFullNamePartnew=this.global.passwordContainsFullNamePartnew;

  }


 checkValueTrue(){
  this.CheckValueNameorProduct = this.passwordContainsFullNamePartnew == true?'Password should not contain the firstname middlename and lastname.':this.paaswordProductName == true?'Password should not contain the any product name and word like test.':null
 this.getProductorUserName()
  return this.CheckValueNameorProduct
}

getProductorUserName(){
  let Values = document.querySelectorAll('.mat-form-field-outline-thick')
  let BorderColor =Values[2]
  this.CheckValueNameorProduct !=null &&  this.CheckValueNameorProduct !=''? BorderColor.classList.add('Border'):BorderColor.classList.remove('Border')

}
  @HostListener('window:keydown', ['$event'])
  keyDownFunction(event) {
    if (event.keyCode === 13) {
      this.btnEnabled = document.getElementsByClassName('submitPwd');
      for (let key in this.btnEnabled) {
        if (this.btnEnabled[key].disabled === false) {
          this.changePwd();
        }
      }
    }
  }

  changePwd(){
    this.submitBtnClicked = true;
    const inputDetails = { emailId: this.form.value.email, password: this.form.value.oldpwd, newPassword: this.form.value.password};
    this.services.forceToChangePwd(inputDetails).subscribe((data: any) => {
      if (data) {
        this.services.callSnackbaronSuccess(this.global.Password_changed_successfully);
        this.router.navigateByUrl('/login');

      } else {
        this.services.callSnackbaronError(this.global.Please_try_again);
        this.submitBtnClicked = false;
      }
    }, error => {
      console.log(error.error);
      this.services.callSnackbaronError(error.error);
      this.submitBtnClicked = false;
    }
    )
  }

}

const confirmPassword = (controlName: string, matchingControlName: string) => {
  return async (formGroup: FormGroup) => {
    const control = formGroup.controls[controlName];
    const matchingControl = formGroup.controls[matchingControlName];
    if (matchingControl.errors && !matchingControl.errors.mustMatch)
      return;

    return control.value !== matchingControl.value ? matchingControl.setErrors({ mustMatch: true }) : matchingControl.setErrors(null);
  }
}
