import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import icClose from '@iconify/icons-ic/twotone-close';
import { GlobalService } from 'src/app/global.service';

@Component({
  selector: 'exai-terms-dialog',
  templateUrl: './terms-dialog.component.html',
  styleUrls: ['./terms-dialog.component.scss']
})

export class TermsDialogComponent implements OnInit {
  icClose = icClose;
  ShowCSATBoolean:boolean = false
  titleHeight: any;
  terms: Array<Object> = [
    {
      title: this.global.General_Terms_Conditions,
      id: 1,
    },
    {
      title: this.global.Acceptable_UsePolicy,
      id: 2,
    },
    {
      title: this.global.Cancellation_Policy,
      id: 3,
    },
    {
      title: this.global.Privacy_Policy,
      id: 4
    }
  ];

  constructor(private dialogRef: MatDialogRef<TermsDialogComponent>, public global: GlobalService,
    @Inject(MAT_DIALOG_DATA) public data: any) {
      this.ShowCSATBoolean =data == 1?true:false
   }

  ngOnInit(): void {

    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        const id = entry.target.getAttribute('id');
        if (entry.intersectionRatio > 0) {
          document.querySelector(`.terms${id}`).classList.add('active');
        } else {
          document.querySelector(`.terms${id}`).classList.remove('active');
        }
      });
    });

    // Track all sections that have an `id` applied
    document.querySelectorAll('section[id]').forEach((section) => {
      observer.observe(section);
    });
  }

  makeActive(id) {
    document.querySelector(`.terms${id}`).classList.add('active');
    document.getElementById(id).scrollIntoView();
  }

  close() {
    this.dialogRef.close();
  }
}
