.mat-icon,
.mat-icon-button .mat-icon {
  font-size: var(--default-icon-size);
  height: unset;
  width: unset;
}

.ic-inline>svg {
  display: inline-block;
}

ic-icon:not(.ic-inline)>svg,
.iconify:not(.ic-inline)>svg {
  margin: 0 auto;
  vertical-align: middle;
}

.exai-scrollblock {
  position: fixed;
  width: 100%;
}

// Login form overrides

.card {
  box-shadow: none;
}

.loginFormWidth.max-w-xs {
  max-width: 25rem;
}

.loginInput .mat-form-field-wrapper {
  padding-bottom: 1em;
}

.mat-form-field-appearance-outline .mat-form-field-outline {
  color: var(--card-border);
}

.loginInput .mat-form-field-appearance-outline .mat-form-field-outline-thick {
  color: var(--primary);
}

.loginInput .mat-form-field-prefix .mat-icon,
.mat-form-field-suffix .mat-icon {
  font-size: 25px;
}

.loginInput .mat-form-field.mat-focused .mat-form-field-label {
  // color: var(--primary);
}

.loginInput .mat-form-field-infix {
  border-top: 0.5em solid transparent;
}

.loginInput .mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.8em 0 0.8em 0;
}

.loginInput .mat-form-field-label {
  font-size: 13px;
}

.loginInput .mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-start,
.loginInput .mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-end,
.loginInput .mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-gap {
  border-width: 1px;
}

.loginBtn.mat-raised-button.mat-button-disabled.mat-button-disabled {
  color: var(--white);
}

.mat-form-field-suffix .mat-icon {
  font-size: 20px;
}

.loginInput mat-icon {
  color: var(--login-icons);
}

.eyeIcon .mat-form-field-suffix .mat-icon {
  cursor: pointer;
}

// Register form overrides

// .loginInput .mat-form-field-label,
.registerInput .mat-form-field-infix {
  width: 50px;
}

.registerInput .mat-form-field-label {
  font-size: 11px;
}

.registerInput .mat-form-field-appearance-outline .mat-form-field-wrapper {
  // margin: .1em 0;
}

.loginBtn.mat-flat-button.mat-primary.mat-button-disabled,
.loginBtn.mat-flat-button.mat-button-disabled.mat-button-disabled {
  color: var(--white);
}

.registerInput .mat-error {
  // font-size: 8px;
}

.registerInput .mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button {
  // width: 0.5em;
  // height: 0.5em;
}

.registerInput .mat-form-field-appearance-outline .mat-select-arrow-wrapper {
  transform: none;
}

.registerInput .mat-form-field {
  font-size: 12px;
  margin-top: 0.5rem;
}

// .loginInput .mat-form-field-appearance-outline .mat-form-field-infix,
.registerInput .mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.7em 0 0.7em 0;
}

.registerInput .mat-form-field-infix {
  // width: 50px;
}

.registerInput .mat-button-wrapper {
  font-size: 11px;
}

.loginInput .mat-form-field-appearance-outline .mat-form-field-outline-start,
.registerInput .mat-form-field-appearance-outline .mat-form-field-outline-start {
  border-radius: 3px 0 0 3px;
}

.loginInput .mat-form-field-appearance-outline .mat-form-field-outline-end,
.registerInput .mat-form-field-appearance-outline .mat-form-field-outline-end {
  border-radius: 0 3px 3px 0;
}

.mat-form-field .mat-select.mat-select-invalid .mat-select-arrow {
  color: var(--card-border);
}

.registerInput .mat-select-placeholder {
  color: var(--card-border);
}

.mat-form-field-label {
  color: var(--card-border);
}

.registerInput .mat-form-field-appearance-outline .mat-form-field-infix {
  // padding: 1em 0 1em 0;
}

.mat-form-field-appearance-outline .mat-form-field-outline-thick {
  // color: #209e91;
}

.registerInput .mat-form-field-appearance-outline.mat-form-field-disabled .mat-form-field-outline {
  color: var(--register-icon);
}

.registerInput .mat-datepicker-toggle {
  color: var(--card-border);
}

.registerInput .mat-select-arrow {
  color: var(--card-border);
}

.registerInput .mat-form-field-suffix .mat-icon {
  font-size: 17px;
}

.orgIcon.mat-primary.mat-form-field .mat-form-field-suffix {
  transform: rotate(180deg);
}

.orgIcon .mat-form-field-suffix .mat-icon {
  font-size: 14px;
}

.orgIcon .mat-form-field-prefix .mat-icon,
.orgIcon .mat-form-field-suffix .mat-icon {
  line-height: 1.25;
}

.registerInput mat-icon {
  color: var(--register-icon);
}

.registerInput .mat-form-field.mat-focused.mat-primary .mat-select-arrow {
  color: var(--card-border);
}

.dobInput .mat-primary .mat-option.mat-selected:not(.mat-option-disabled) {
  color: var(--primary);
}

.orgIcon {
  .mat-input-element:disabled {
    color: var(--card-border);
  }
}

.genderInput .mat-select-value {
  color: var(--forgot-password-heading);
  // var(--card-border);
}

.genderInput .mat-primary .mat-option.mat-selected:not(.mat-option-disabled) {
  color: rgba(var(--color-primary), 1);
}

// Verify Email

.verifyEmailWidth.max-w-xs {
  max-width: 25rem;
}

.strokedBtn.mat-stroked-button:not(.mat-button-disabled) {
  border-color: var(--primary);
}

.strokedBtn {
  color: var(--primary);
  font-size: 12px;
}

.strokedBtn .mat-button-focus-overlay {
  background: transparent;
}

// .strokedBtn .mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay {
//   opacity: 0;
// }

// Forgot Password

.leftArrow.mat-icon {
  height: 19px;
  width: 19px;
}

// Terms Dialog
.my-custom-dialog-class .mat-dialog-container {
  border: 0.3px solid var(--card-border);
}

// Mat Dialog

.my-custom-dialog-class .mat-dialog-container {
  overflow: hidden;
}

.my-custom-dialog-class1 .mat-dialog-container {
  overflow: hidden;
  padding: 0px !important;
}



element.style {
  transform: scale(1);
  opacity: 1;
}

// Snackbar styling
.wider-snackbar {
  max-width: 600px !important; // Increase from default
  min-width: 400px !important; // Set minimum width

  // Center the snackbar
  &.mat-snack-bar-container {
    margin: 0 auto !important;
  }

  // Improve text readability
  .mat-simple-snackbar {
    font-size: 14px;
    line-height: 1.5;
  }
}

// Existing snackbar styles
.error-snackbar.mat-snack-bar-container {
  color: var(--snackbar-text);
  background: var(--white);
  box-shadow: none;
  border: 0.3px solid var(--snackbar-error);
  min-width: 0;
}

.warning-snackbar.mat-snack-bar-container {
  color: var(--snackbar-text);
  background: var(--white);
  box-shadow: none;
  border: 0.3px solid var(--snackbar-warning);
  min-width: 0;
}

.success-snackbar.mat-snack-bar-container {
  color: var(--snackbar-text);
  background: var(--white);
  box-shadow: none;
  border: 0.3px solid var(--primary);
  min-width: 0;
}

.success-snackbar .mat-icon-button,
.warning-snackbar .mat-icon-button,
.error-snackbar .mat-icon-button {
  padding: 0 16px;
  min-width: 0;
  width: 40px;
  height: 24px;
  flex-shrink: 0;
  line-height: 26px;
  border-radius: unset;
}

.success-snackbar .grid-auto:first-child {
  background-image: url(../../../assets/img/demo/success1.svg);
  background-repeat: no-repeat;
  background-position: left center;
  background-size: 36px 100px;
}

.warning-snackbar .grid-auto:first-child {
  background-image: url(../../../assets/img/demo/Group-396.svg);
  background-repeat: no-repeat;
  background-position: left center;
  background-size: 36px 100px;
}

.error-snackbar .grid-auto:first-child {
  background-image: url(../../../assets/img/demo/Group-397.svg);
  background-repeat: no-repeat;
  background-position: left center;
  background-size: 36px 100px;
}

// css for ngx-mat-intl-tel-input

input.country-search {
  padding: 0px 5px 9px !important;
}

.mat-menu-panel {
  padding: 8px 8px 8px 8px;
  max-width: 416px !important;
  max-height: calc(100vh - 55vh) !important;
  overflow-x: hidden !important;
}

.country-list-button {
  font-size: 12px !important;
  padding: 12px 9px !important;
}

input.country-search:focus-visible {
  outline: -webkit-focus-ring-color auto 0px !important;
}

// Snackbar
.mat-snack-bar-handset {
  width: auto !important;
}

// Header

.formsHeader {
  position: absolute;
  top: 0;
  width: 100%;
}