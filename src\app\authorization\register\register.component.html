<div class="bg-pattern bottomSpace" *ngIf="!registrationCompleted">
    <div class="w-full login" fxLayout="column" fxLayoutAlign="center center">
        <div class="toolbar w-full px-gutter py-3" fxLayout="row" fxLayoutAlign="start center" exaiContainer>
            <a class="ltr:mr-4 rtl:ml-4 block" fxLayout="column" fxLayoutAlign="start start">
                <img class="w-48 select-none" src="assets/img/demo/logo.svg">
            </a>
            <ng-container *ngIf="loadingObs | async">
                <div class="card-div" fxLayout="column" fxLayoutAlign="center center">
                    <div class="spinner w-full">
                        <div class="bounce1"></div>
                        <div class="bounce2"></div>
                        <div class="bounce3"></div>
                    </div>
                </div>
            </ng-container>
            <div class="ltr:mr-4 rtl:ml-4 block flagLogo" fxLayout="column" fxLayoutAlign="end end" style="padding-right: 16px;">
                <div style="padding-right: 10px;">
                    <button mat-icon-button (click)="redirectToCredentialTicketForm()" class="ButtonChat"  matTooltip=
                    "Email Support">
                    <mat-icon class="email">email</mat-icon>
                      <!-- Sign Up -->
                    </button>
                    </div>
                <img class="w-5 select-none" src="assets/img/demo/US.svg">
            </div>
            <!-- <div class="ltr:mr-4 rtl:ml-4 block flagLogo" fxLayout="column" fxLayoutAlign="end end">
                <img class="w-5 select-none" src="assets/img/demo/US.svg">
            </div> -->
        </div>
        <div class="card overflow-hidden w-full max-w-md">
            <div class="" >
                <ng-container *ngFor="let state of states">
                    <div class="p-2" fxLayout="column" fxLayoutAlign="center center" *ngIf="clientStateCode == state.stateCode">
                        <div class="state-name" fxLayout="column" fxLayoutAlign="center center" >
                            {{state.stateName}}
                        </div>
                        <h6 class="note text-center t-xs"> <b> Important: </b>This signup page is for {{state.stateName}} credentia platform users only. Please visit <a class="headerline" href="https://credentia.com/test-takers" target='_blank'>here</a>  for other state's credentia platform signup. If you are an existing user, please click <a class="headerline" href="https://credentiauat.examroom.ai/login/" target='_blank'>here.</a></h6>
                    </div>
                </ng-container>
                <hr class="">
                <div class="p-6 pb-0" fxLayout="column" fxLayoutAlign="center center">
                    <h3 class="text-center">{{global.Create_Your_Account}}<i
                            class="material-icons lockOutline">lock_outline</i></h3>
                </div>
                <div class="p-6" fxLayout="column" fxLayoutAlign="">
                    <form [formGroup]='form'>
                        <div class="pb-0" fxLayout="column">
                            <div class="subheading-1 pb-2">{{global.General_Information}}</div>
                            <mat-error class="mb-2 text-xs" *ngIf="textNameAsGovtId && StateCode !='NC' && textNameAsGovtId && StateCode !=='WA'"> Enter your name exactly as it appears on your government-issued identification.</mat-error>
                            <mat-error class="mb-2 text-xs" *ngIf="textNameAsGovtId && StateCode ==='NC'">Enter your name exactly as it appears on your government-issued identification.  <br>If you’ve previously been listed in the North Carolina Nurse Aide I Registry or Medication Aide Registry, please enter your FIRST, MIDDLE and LAST NAME as it appears in the registries, including hyphens and suffixes. Click<a class="color1"href="https://info.ncdhhs.gov/dhsr/hcpr/index.html" target="_blank"> here</a> , to view your name in the registries.<br>  If your name is not listed correctly in the registries, please contact the Registry Office, with the Department of Health and Human Services, at ************ prior to registering for any examination.</mat-error>
                            <mat-error class="mb-2 text-xs" *ngIf="textNameAsGovtId && StateCode ==='WA'">Please provide your full legal name on this form. "Legal name" is the name appearing on your official certificate of birth or, if your name has changed since birth, on an official marriage certificate or an order by a court. Please makesure the name you provide on this form is consistent with the name you used on your NAC license application with the Washington State Department of Health. </mat-error>
                            <div class="loginInput registerInput" fxLayout="row" fxLayout.lt-sm="column"
                                fxLayoutGap="16px" fxLayoutGap.lt-sm="0">
                                <mat-form-field fxFlex='50%' class="exai-flex-form-field" appearance="outline">
                                    <mat-label>{{global.First_name}}</mat-label>
                                    <input type="text" (keyup)="first($event)" class="form-control" matInput placeholder="{{global.First_name}}"
                                        formControlName="firstName" [matTooltip]="'Legal '+global.First_name" >
                                    <mat-error *ngFor="let validation of validation_messages.firstName">
                                        <mat-error class="error-message"
                                            *ngIf="form.get('firstName').hasError(validation.type) && (form.get('firstName').dirty || form.get('firstName').touched)">
                                            {{validation.message}}</mat-error>
                                    </mat-error>
                                </mat-form-field>
                                <mat-form-field fxFlex='50%' class="exai-flex-form-field" appearance="outline">
                                    <mat-label>{{global.Middle_name}}</mat-label>
                                    <input type="text" (keyup)="middle($event)"class="form-control" matInput
                                        placeholder="{{global.Middle_name}}" formControlName="middleName" [matTooltip]="'Legal '+global.Middle_name">
                                    <mat-error *ngFor="let validation of validation_messages.middleName">
                                        <mat-error class="error-message"
                                            *ngIf="form.get('middleName').hasError(validation.type) && (form.get('middleName').dirty || form.get('middleName').touched)">
                                            {{validation.message}}</mat-error>
                                    </mat-error>
                                </mat-form-field>
                            </div>
                            <div class="loginInput registerInput" fxLayout="row" fxLayout.lt-sm="column"
                                fxLayoutGap="16px" fxLayoutGap.lt-sm="0">
                                <mat-form-field fxFlex='48%' class="exai-flex-form-field" appearance="outline">
                                    <mat-label>{{global.Last_name}}</mat-label>
                                    <input type="text" (keyup)="last($event)"class="form-control" matInput placeholder="{{global.Last_name}}"
                                        formControlName="lastName" [matTooltip]="'Legal '+global.Last_name">
                                    <mat-error *ngFor="let validation of validation_messages.lastName">
                                        <mat-error class="error-message"
                                            *ngIf="form.get('lastName').hasError(validation.type) && (form.get('lastName').dirty || form.get('lastName').touched)">
                                            {{validation.message}}</mat-error>
                                    </mat-error>
                                </mat-form-field>
                                <ng-container *ngIf="showorgIDField">
                                    <mat-form-field fxFlex='50%' class="exai-flex-form-field" appearance="outline"
                                        class="genderInput">
                                        <mat-select formControlName="orgID"
                                            placeholder="{{this.orgIDValue ? this.orgIDValue : this.global.Organization_Code}}" [matTooltip]="'Legal '+global.Organization_Code">
                                            <mat-option *ngFor="let org of organizations" [value]="org">
                                                {{ org }}
                                            </mat-option>
                                        </mat-select>
                                        <mat-error *ngFor="let validation of validation_messages.orgID">
                                            <mat-error class="error-message"
                                                *ngIf="form.get('orgID').hasError(validation.type) && (form.get('orgID').dirty || form.get('orgID').touched)">
                                                {{validation.message}}</mat-error>
                                        </mat-error>
                                    </mat-form-field>
                                </ng-container>
                            </div>
                            <div class="subheading-1 py-2">{{global.Other_Information}}</div>
                            <mat-error class="mb-2 text-xs" *ngIf="textNameAsGovtId && StateCode ==='WA'">If you do not have a SSN: For an alternate ID to test only, please e-mail <EMAIL> with your full legal name; date of birth; full address including city, state and zip code; phone number, and email. Email subject line should say “Request for Alternate ID to SS#”</mat-error>
                            <div class="loginInput registerInput" fxLayout="row" fxLayout.lt-sm="column"
                                fxLayoutGap="16px" fxLayoutGap.lt-sm="0" *ngIf="!isSponsor">
                                <mat-form-field class="exai-flex-form-field" fxFlex='50%' appearance="outline"
                                    class="dobInput">
                                    <mat-label>{{global.Date_of_Birth}}</mat-label>
                                    <input matInput [max]="maxDate" [matDatepicker]="picker" formControlName="dob" (dateChange)="onDateChange($event)"
                                        placeholder="{{global.Date_of_Birth}}" [matTooltip]="'Legal '+global.Date_of_Birth" [disabled]="form.get('dob').disabled" DateFilterDirective>
                                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                                    <mat-datepicker   #picker [disabled]="form.get('dob').disabled"></mat-datepicker>
                                    <mat-error *ngFor="let validation of validation_messages.dob">
                                        <mat-error class="error-message"
                                            *ngIf="form.get('dob').hasError(validation.type) && (form.get('dob').dirty || form.get('dob').touched)">
                                            {{validation.message }}</mat-error>
                                         
                                    </mat-error>
                                
                                
                                </mat-form-field>
                                <!-- <mat-form-field class="exai-flex-form-field" fxFlex='50%' appearance="outline" class="dobInput">
                                    <mat-label>{{global.Date_of_Birth}}</mat-label>
                                    <input matInput [max]="maxDate" [matDatepicker]="picker" formControlName="dob" (dateChange)="onDateChange($event)"
                                           placeholder="{{global.Date_of_Birth}}" [disabled]="form.get('dob').disabled" DateFilterDirective>
                                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                                    <mat-datepicker #picker [disabled]="form.get('dob').disabled"></mat-datepicker>
                                    <mat-error *ngFor="let validation of validation_messages.dob">
                                      <mat-error class="error-message"
                                                 *ngIf="form.get('dob').hasError(validation.type) && (form.get('dob').dirty || form.get('dob').touched)">
                                        {{validation.message }}
                                      </mat-error>
                                    </mat-error>
                                  </mat-form-field> -->
                                  
                             <!-- <div *ngIf="dobInvalid && StateCode=='GA'" >
                                <mat-error  class="textsize">{{ErrorMessage}} </mat-error> 
                            </div> -->
                                <mat-form-field fxFlex='50%' class="exai-flex-form-field" appearance="outline"
                                    class="genderInput">
                                    <mat-select formControlName="gender" placeholder="{{global.Gender}}" [matTooltip]="'Legal '+global.Gender">
                                        <mat-option *ngFor="let gender of genders" [value]="gender">
                                            {{ gender }}
                                        </mat-option>
                                    </mat-select>
                                    <mat-error *ngFor="let validation of validation_messages.gender">
                                        <mat-error class="error-message"
                                            *ngIf="form.get('gender').hasError(validation.type) && (form.get('gender').dirty || form.get('gender').touched)">
                                            {{validation.message}}</mat-error>
                                    </mat-error>
                                </mat-form-field>
                            </div>
                            <div class="loginInput registerInput" fxLayout="row" fxLayout.lt-sm="column"
                                fxLayoutGap="16px" fxLayoutGap.lt-sm="0">
                                <mat-form-field fxFlex='100%' class="exai-flex-form-field" appearance="outline">
                                    <mat-label>{{global.Address}}</mat-label>
                                    <input type="text" class="form-control" matInput placeholder="{{global.Address}}"
                                        formControlName="address" [matTooltip]="'Legal '+global.Address">
                                    <mat-error *ngFor="let validation of validation_messages.address">
                                        <mat-error class="error-message"
                                            *ngIf="form.get('address').hasError(validation.type) && (form.get('address').dirty || form.get('address').touched)">
                                            {{validation.message}}</mat-error>
                                    </mat-error>
                                </mat-form-field>
                            </div>
                            <div class="loginInput registerInput" fxLayout="row" fxLayout.lt-sm="column"
                                fxLayoutGap="16px" fxLayoutGap.lt-sm="0">
                                <mat-form-field fxFlex='50%' class="exai-flex-form-field" appearance="outline">
                                    <mat-label>{{global.City}}</mat-label>
                                    <input type="text" class="form-control" matInput placeholder="{{global.City}}"
                                        formControlName="city" [matTooltip]="'Legal '+global.City">
                                    <mat-error *ngFor="let validation of validation_messages.city">
                                        <mat-error class="error-message"
                                            *ngIf="form.get('city').hasError(validation.type) && (form.get('city').dirty || form.get('city').touched)">
                                            {{validation.message}}</mat-error>
                                    </mat-error>
                                </mat-form-field>
                                <mat-form-field fxFlex='50%' class="exai-flex-form-field" appearance="outline">
                                    <mat-label>{{global.Zip_Code}}</mat-label>
                                    <input type="text" class="form-control" (keypress)="keyPressNumbers($event)"
                                        matInput placeholder="{{global.Zip_Code}}" formControlName="zipCode" [matTooltip]="'Legal '+global.Zip_Code">
                                    <mat-error *ngFor="let validation of validation_messages.zipCode">
                                        <mat-error class="error-message"
                                            *ngIf="form.get('zipCode').hasError(validation.type) && (form.get('zipCode').dirty || form.get('zipCode').touched)">
                                            {{validation.message}}</mat-error>
                                    </mat-error>
                                </mat-form-field>
                            </div>
                            <div class="loginInput registerInput" fxLayout="row" fxLayout.lt-sm="column"
                                fxLayoutGap="16px" fxLayoutGap.lt-sm="0">
                                <mat-form-field fxFlex='100%' class="exai-flex-form-field" appearance="outline"
                                    class="genderInput stateInput" >
                                    <mat-select formControlName="state" #mySelect placeholder="{{global.State}}"
                                        (selectionChange)='onOptionsSelected(mySelect.value)' [matTooltip]="'Legal '+global.State">
                                        <mat-option *ngFor="let state of states" [value]="state.stateName">
                                            {{ state.stateName }}
                                        </mat-option>
                                    </mat-select>
                                    <mat-error *ngFor="let validation of validation_messages.state">
                                        <mat-error class="error-message"
                                            *ngIf="form.get('state').hasError(validation.type) && (form.get('state').dirty || form.get('state').touched)">
                                            {{validation.message}}</mat-error>
                                    </mat-error>
                                </mat-form-field>
                            </div>
                            <div class="loginInput registerInput" fxLayout="row" fxLayout.lt-sm="column"
                                fxLayoutGap="16px" fxLayoutGap.lt-sm="0">
                                <mat-form-field fxFlex='50%' class="exai-flex-form-field" appearance="outline"
                                *ngIf="!isSponsor">
                                <mat-label *ngIf="clientStateCode == 'CO'; else ssnLabel"> SSN or ITIN</mat-label>
                                <ng-template #ssnLabel>
                                    <mat-label>{{global.SSN}}</mat-label>
                                </ng-template>
                                <input  (copy)="preventDefault($event)" (cut)="preventDefault($event)" type="text" (keypress)="keyPressNumbers($event)" class="form-control"
                                    matInput [placeholder]="clientStateCode == 'CO'? 'SSN or ITIN' : global.SSN" formControlName="ssn" [matTooltip]="'Legal '+global.SSN">
                                <mat-error *ngFor="let validation of clientStateCode == 'CO'? validation_messages.ssnColorado : validation_messages.ssn">
                                    <mat-error class="error-message"
                                        *ngIf="form.get('ssn').hasError(validation.type)  && (form.get('ssn').dirty || form.get('ssn').touched)">
                                        {{ validation.message}}</mat-error>
                                </mat-error>
                            </mat-form-field>
                                <mat-form-field fxFlex='50%' class="exai-flex-form-field" appearance="outline"
                                    *ngIf="!isSponsor">
                                    <mat-label *ngIf="clientStateCode == 'CO'; else ssnLabel"> SSN or ITIN</mat-label>
                                    <ng-template #ssnLabel>
                                        <mat-label>{{global.Confirm_Ssn}}</mat-label>
                                    </ng-template>
                                    <input type="text" (keypress)="keyPressNumbers($event)" class="form-control"
                                    [autocomplete]="false"
                                    type="password" 
                                        matInput [placeholder]="clientStateCode == 'CO'? 'Confirm SSN or ITIN' : global.Confirm_Ssn" formControlName="confirmSsn" [matTooltip]="'Legal '+global.SSN">
                                        
                                            <mat-error class="error-message mb-2"
                                        *ngIf="form.get('confirmSsn').touched && !form.get('confirmSsn').valid && clientStateCode == 'CO'"
                                        >
                                        {{global.SsnItin_not_matched}}
                                    </mat-error>
                                        <mat-error class="error-message mb-2"
                                    *ngIf="form.get('confirmSsn').touched && !form.get('confirmSsn').valid && clientStateCode != 'CO'"
                                    >
                                    {{global.Ssn_not_matched}}
                                </mat-error>
                                    

                                    
                                </mat-form-field>
                            </div>
                            <div class="loginInput registerInput" fxLayout="row" fxLayout.lt-sm="column"
                                fxLayoutGap="16px" fxLayoutGap.lt-sm="0">
                                <mat-form-field fxFlex='50%' class="exai-flex-form-field phNumberInput"
                                    appearance="outline">
                                    <mat-label>{{global.Phone_number}}</mat-label>
                                    <ngx-mat-intl-tel-input class="form-control" phNumberInput matInput
                                        placeholder="{{global.Phone_number}}" [preferredCountries]="['us']"
                                        [enablePlaceholder]="false" [enableSearch]="true" name="phone"
                                        formControlName="phoneNumber" [matTooltip]="global.Phone_number">
                                    </ngx-mat-intl-tel-input>
                                    <mat-error *ngFor="let validation of validation_messages.phoneNumber">
                                        <mat-error class="error-message"
                                            *ngIf="form.get('phoneNumber').hasError(validation.type) && (form.get('phoneNumber').dirty || form.get('phoneNumber').touched)">
                                            {{validation.message}}</mat-error>
                                    </mat-error>
                                </mat-form-field>
                                <mat-form-field fxFlex='50%' class="exai-flex-form-field" appearance="outline">
                                    <mat-label>{{global.email}}</mat-label>
                                    <input type="email" class="form-control" matInput placeholder="{{global.email}}"
                                        formControlName="_email" [matTooltip]="global.email">
                                    <mat-error *ngFor="let validation of validation_messages._email">
                                        <mat-error class="error-message"
                                            *ngIf="form.get('_email').hasError(validation.type) && (form.get('_email').dirty || form.get('_email').touched)">
                                            {{validation.message}}</mat-error>
                                    </mat-error>
                                </mat-form-field>
                              
                            </div>
                            <div class="loginInput registerInput" fxLayout="row" fxLayout.lt-sm="column"
                            fxLayoutGap="16px" fxLayoutGap.lt-sm="0" *ngIf="showregisterfield">
                            <mat-form-field fxFlex='50%' class="exai-flex-form-field" appearance="outline">
                                <mat-label>{{global.pa_nurse_aide}}</mat-label>
                                <input type="text" class="form-control" matInput placeholder="{{global.pa_nurse_aide}}"   (input)="userMetrics($event.target.value)" (keypress)="keyPressNumbers($event)"
                                    formControlName="Registrationnumber"  [matTooltip]="'Legal '+global.pa_nurse_aide">
                              
                            </mat-form-field>

                            <mat-error *ngIf="showregisterfield" style="display: inline-flex !important;"  >
                                <mat-icon class="mat-icon mt-4" *ngIf="errors && CanMirate ==null && form.value.Registrationnumber !=''">close</mat-icon>
                                <mat-icon  class="done mt-4" *ngIf="CanMirate && errors && form.value.Registrationnumber !=''">done</mat-icon>
                                <span>
                                   
                                    <mat-error class=" ml-2 mt-2" *ngIf="form.value.Registrationnumber !=''"  [ngClass]="CanMirate == true ? 'done' : 'close' " [innerHtml]="errors" >
                                        </mat-error>
                                </span>
                            </mat-error>
                            <p class="contentdone" *ngIf="showregisterfield && form.value.Registrationnumber ==''">(Optional)</p>
             
</div>


                            <div class="subheading-1  py-2">{{global.Account_Setup}}</div>
                            <div class="loginInput registerInput" fxLayout="row" fxLayout.lt-sm="column"
                                fxLayoutGap="16px" fxLayoutGap.lt-sm="0">
                                <mat-form-field fxFlex='50%' class="exai-flex-form-field" appearance="outline">
                                    <mat-label>{{global.Enter_Password}}</mat-label>
                                    <input   (keyup)="passText($event)"tooltipClass="edupala-tooltip"(input)="passEv1($event)" [tooltip]="$any(HtmlContent)" theme="light"
                                        contentType="template" placement="right" type="password" class="form-control"
                                        matInput placeholder="{{global.Enter_Password}}" formControlName="password" >
                                    <mat-error *ngFor="let validation of validation_messages.password">
                                        <mat-error class="error-message font-medium"
                                            *ngIf="form.get('password').hasError(validation.type) && (form.get('password').dirty || form.get('password').touched)">
                                            {{validation.message}}</mat-error>
                                    </mat-error>
                                </mat-form-field>
                               
                                <mat-form-field fxFlex='50%' class="exai-flex-form-field" appearance="outline">
                                    <mat-label>{{global.Confirm_Password}}</mat-label>
                                    <input type="password" class="form-control" matInput
                                        placeholder="{{global.Confirm_Password}}" formControlName="confirmPassword">
                                    <mat-error class="error-message mb-2"
                                        *ngIf="form.get('confirmPassword').touched && !form.get('confirmPassword').valid">
                                        {{global.Passwords_not_matched}}
                                    </mat-error>
                                </mat-form-field>
                            </div>
                            <div  class="error_font_size  font-medium mt-4 ml-2 w-48" *ngIf=" form.value.password != '' " >
                               {{checkValueTrue()}} 
                            </div>
                        
                            <!-- Tooltip for Password -->
                            <ng-template #HtmlContent>
                                <h6 class="text-primary pb-1">Password must:</h6>
                                <ul class="ml-4 text-size">
                                    <li class="mt-1">Have at  least 12 characters, includes one number, contains one special character, has at least one uppercase letter, and includes at least one lowercase letter.</li>
                                    <li class="mt-1">Should not contain the  Firstname or Middlename or Lastname or product name or word like test</li>
                                </ul>
                            </ng-template>

                            <div class="mt-4 addingtextMargin" fxLayout="row" fxLayoutAlign="center center">
                                <mat-checkbox   (change)='showOptions($event)' class="align-co" color='primary' >
                            </mat-checkbox> <div class="terms ml-2 mt-1">{{global.By_clicking_SignUp}} <a
                                (click)="openDialog()">{{global.Terms_and_DataPolicy}}</a>
                        </div>
                            </div>
                             
                                
                            <div fxLayout="row" fxLayoutAlign="center center">
                                <button mat-flat-button color="primary" [disabled]="!checkStatus() || signUpBtnClicked || passwordContainsFullNamePartnew == true ||paaswordProductName ==true || !isChecked "
                              
                               
                                    class="loginBtn" (click)="register()"  *ngIf="ShowSignUp || form.value.Registrationnumber ==''"
                                    (keydown)="keyDownFunction($event)">{{global.Sign_Up}}</button>
                            </div>
                            <div class="haveAccount mt-2" fxLayout="row" fxLayoutAlign="center center">
                                {{global.Already_have_an_account}}<a [routerLink]="''"
                                    [queryParams]="{ RoleId: roleIdfromQueryParamstoSignInPage, TenantCode: tenantCodefromQueryParamstoSignInPage, StateCode: stateCodefromQueryParamstoSignInPage}">&nbsp;{{global.Sign_In}}</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="bg-pattern bottomSpace" *ngIf="registrationCompleted">
    <div class="w-full " fxLayout="column" fxLayoutAlign="center center">
        <div class="toolbar w-full px-gutter py-3" fxLayout="row" fxLayoutAlign="start center" exaiContainer>
            <a class="ltr:mr-4 rtl:ml-4 block" fxLayout="column" fxLayoutAlign="start start">
                <img class="w-48 select-none" src="assets/img/demo/logo.svg">
            </a>
            <div class="ltr:mr-4 rtl:ml-4 block flagLogo" fxLayout="column" fxLayoutAlign="end end">
                <img class="w-5 select-none" src="assets/img/demo/US.svg">
            </div>
        </div>
        <div class="card overflow-hidden w-full verifyEmailWidth max-w-xs px-8 py-4">
            <div class="p-6 pb-0 loginFormLogo" fxLayout="column" fxLayoutAlign="center center">
                <div class="fill-current text-center">
                    <h3>{{global.Verify_your_email}}</h3>
                </div>
            </div>
            <div class="text-center">
                <p class="title-p mb-8">{{global.You_will_need_to_verify_your_email_to_complete_registration}}</p>
            </div>
            <img class="mb-6 emailImg mx-auto" src="assets/img/demo/email-verification.svg">
            <div fxLayout="row" fxLayoutAlign="center center">
                <p class="emailVerfnDesc">An email has been sent to {{emailID}} with a link to verify and setup your
                    account, if you
                    have not
                    received the email after a few minutes, please check your spam folder.</p>
                <br>
            </div>
            <div class="emailVerfnDesc font-bold">
                <NAME_EMAIL> to your safe senders list.
            </div>
            <div class="pt-2 text-center">
                <a class="incorrectEmail mt-4" (click)="incorrectEmail()">{{global.Incorrect_Email}}</a>
            </div>
            <div class="p-6 text-center">
                <!-- <button mat-stroked-button class="strokedBtn float-end mb-2 mr-6" (click)="resendEmail()"
                *ngIf="!isUserVerifiedLink">{{global.Resend_Email}}</button> -->
                <button mat-stroked-button class="strokedBtn float-end mb-2 mr-6" *ngIf="!isUserVerifiedLink"
                    [routerLink]="''">{{global.Back_to_Login}}</button>
                <button mat-stroked-button class="strokedBtn float-start"
                    (click)="openDialogforContactSupport()">{{global.Contact_Support}}</button>
            </div>
        </div>
    </div>
</div>

<exai-footer class="exai-footer" style="position: absolute;"></exai-footer>