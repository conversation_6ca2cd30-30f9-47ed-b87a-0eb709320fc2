import { Injectable } from "@angular/core";

@Injectable({
  providedIn: "root",
})
export class GlobalService {
  resetPasswordTest;
  namePartRegex;
  paaswordProductName: boolean = false;
  passTextevent;
  firstNameevent;
  middleNameevent;
  lastNameevent;
  passwordContainsFullNamePartnew: boolean = false;
  isMiddlestring: boolean = false;
  isLaststring: boolean = false;
  constructor() {}
  // clientRoleID
  Candidate_RoleID: number = 1;
  Client_RoleID: number = 2;
  SuperAdmin_RoleID: number = 13;
  OperationStaff_RoleID: number = 17;
  StateClient_RoleID: number = 14;
  TrainingInstitute_RoleID: number = 15;
  Sponser_RoleID: number = 18;
  SupportStaff: number = 8;
  stateId: number = 16;
  Employer: number = 19;
  FinanaceRole: number = 21;
  PayRole: number = 26;
  Hrms: number = 25;
  operationaccount: number = 27;
  Evalator: number = 22;
  testsite: number = 23;
  Proctor: number = 4;
  QAC: number = 28;

  // clientID's to remove validation for SSN
  stateCodestoClearSSNValidation: Array<String> = ["PA"];

  PAStateId = 4;

  // "Enter your name exactly as it appears on your government-issued identification." remove for sponsor in register form
  textInRegisterFormasGovtID: Array<String> = ["18"];
  // login
  email: string = "Email";
  pa_nurse_aide = "PA Nurse Aide Registration Number";
  password: string = "Password";
  login: string = "Login";
  forgot_Pwd: string = "Forgot Password?";
  Or_SignIn_With: string = "or sign in with";
  signUp: string = "Sign up";
  Dont_Have_an_Account: string = "Don't have an account?";
  Please_change_your_pwd: string = "Please change your password";

  // login validation msg's
  Email_is_required = "Email is required";
  Enter_a_valid_email = "Please enter valid email";
  Password_is_required = "Password is required";
  Please_enter_valid_password = "Please enter valid password";

  // login snackbar msg's
  Invalid_credentials_Login_failed = "Invalid credentials, Login failed";
  Please_check_your_credentials = "Please check your credentials";

  // register
  Create_Your_Account: string = "Create Your Account";
  General_Information: string = "General Information";
  First_name: string = "First name";
  Middle_name: string = "Middle name";
  Last_name: string = "Last name";
  Organization_Code: string = "Organization Code";
  Other_Information: string = "Other Information";
  Date_of_Birth: string = "Date of Birth";
  Gender: string = "Gender";
  Address: string = "Address (Number and Street)";
  City: string = "City";
  Zip_Code: string = "Zip Code";
  State: string = "State";
  SSN: string = "SSN";
  Phone_number: string = "Phone number";
  Account_Setup: string = "Account Setup";
  Enter_Password: string = "Enter Password";
  Confirm_Password: string = "Confirm Password";
  By_clicking_SignUp: string = "By clicking Sign Up, you agree to our";
  Terms: string = "Terms";
  Data_Policy: string = "Data Policy";
  Cookie_Policy: string = "Cookie Policy.";
  and: string = "and";
  Sign_Up: string = "Sign Up";
  Sign_In: string = "Sign In";
  Already_have_an_account: string = "Already have an account?";
  Terms_and_DataPolicy: string = "Terms and Data Policy";
  Passwords_not_matched: string = "Passwords didn't matched.";
  Ssn_not_matched: string = "SSN didn't matched.";
  SsnItin_not_matched: string = "SSN or ITIN didn't matched.";
  Confirm_Ssn = "Confirm SSN";

  // register form validation msg's
  // First name
  First_name_is_required: string = "First name is required";
  Maximum_15_characters_are_allowed = "Maximum 15 characters are allowed";
  Maximum_50_characters_are_allowed = "Maximum 50 characters are allowed";
  Please_enter_valid_first_name = "Please enter valid first name";
  // Last name
  Last_name_is_required = "Last name is required";
  Please_enter_valid_last_name = "Please enter valid last name";
  // OrgID
  Please_enter_your_organization_ID = "Organization ID is required";
  // DOB
  Please_select_your_DOB = "DOB is required";
  // gender
  Please_select_your_gender = "Gender is required";
  // address
  Please_enter_your_address = "Address is required";
  Maximum_225_characters_are_allowed = "Maximum 225 characters are allowed";
  // city
  Please_enter_your_city = "City is required";
  Please_enter_valid_City_name = "Please enter valid city name";
  // zipcode
  Zip_code_is_required = "Zip code is required";
  Maximum_5_numbers_are_allowed = "Maximum 5 numbers are allowed";
  // state
  Please_enter_your_state = "State is required";
  Maximum_25_characters_are_allowed = "Maximum 25 characters are allowed";
  Please_enter_valid_state_name = "Please enter valid state name";
  // ssn
  Please_enter_your_Social_Security_number =
    "Social Security Number is required";
  Please_enter_valid_SSN = "Please enter valid SSN";

  // ssn -Colorado
  Please_enter_your_SSN_or_ITIN = "SSN or ITIN is required";
  Please_enter_valid_SSN_or_ITIN = "Please enter valid SSN or ITIN";
  // phNo
  Phone_number_is_required = "Phone number is required";
  Enter_a_valid_phone_number = "Please enter valid phone number";
  // password
  Password_should_not_exceed_99_characters =
    "Password should not exceed 99 characters";
  Use_12_or_more_characters =
    "Use 12 or more characters with a mix of letters, numbers,uppercase ,lowercase & symbols ";

  // register form snackbar msg's
  Registered_Successfully = "Registered Successfully";
  Registration_failed = "Registration failed";
  Please_enter_your_correct_emailID = "Please enter your correct email ID";
  Please_check_your_email_to_verify_the_link =
    "Please check your email to verify the link";

  // verify email
  Verify_your_email: string = "Verify your email";
  You_will_need_to_verify_your_email_to_complete_registration: string =
    "You will need to verify your email to complete registration";
  Incorrect_Email: string = "Incorrect Email ?";
  Resend_Email: string = "Resend Email";
  Contact_Support: string = "Contact Support";
  Verification_Completed: string = "Verification Completed";
  dummy_terms1: string =
    "Lorem ipsum dolor sit amet, consectetuer adipiscing elit.Aeneancommodo ligula eget dolor. Aenean massa. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Donec quam felis, ultricies nec, pellentesque eu, pretium quis, sem. Nulla consequat massa quis enim. Donec pede justo, fringilla vel, aliquet nec, vulputate eget, arcu. In enim justo, rhoncus ut, imperdiet a, venenatis vitae, justo. Nullam dictum felis eu pede mollis pretium. Integer tincidunt. Cras dapibus. Vivamus elementum semper nisi. Aenean vulputate eleifend tellus. Aenean leo ligula, porttitor eu, consequat vitae, eleifend ac, enim. Aliquam lorem ante, dapibus in, viverra quis, feugiat a, tellus. Phasellus viverra nulla ut metus varius laoreet. Quisque rutrum. Aenean imperdiet. Etiam ultricies nisi vel augue. Curabitur ullamcorper ultricies nisi.";
  dummy_terms2: string =
    "Nam pretium turpis et arcu. Duis arcu tortor, suscipit eget, imperdiet nec, imperdiet iaculis, ipsum. Sed aliquam ultrices mauris. Integer ante arcu, accumsan a, consectetuer eget, posuere ut, mauris. Praesent adipiscing.Phasellus ullamcorper ipsum rutrum nunc. Nunc nonummy metus. Vestibulum volutpat pretium libero.Cras id dui. Aenean ut eros et nisl sagittis vestibulum. Nullam nulla eros, ultricies sit amet,nonummy id, imperdiet feugiat, pede. Sed lectus. Donec mollis hendrerit risus. Phasellus nec sem in justo pellentesque facilisis. Etiam imperdiet imperdiet orci. Nunc nec neque. Phasellus leo dolor, tempus non, auctor et, hendrerit quis, nisi. Curabitur ligula sapien, tincidunt non, euismod vitae, posuere imperdiet, leo. Maecenas malesuada. Praesent congue erat at massa. Sed cursus turpis vitae tortor. Donec posuere vulputate arcu. Phasellus accumsan cursus velit.";
  dummy_terms3: string =
    "Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Sed aliquam, nisi quis porttitor congue, elit erat euismod orci, ac placerat dolor lectus quis orci.Phasellus consectetuer vestibulum elit. Aenean tellus metus, bibendum sed, posuere ac, mattis non, nunc. Vestibulum fringilla pede sit amet augue. In turpis. Pellentesque posuere. Praesent turpis.Aenean posuere, tortor sed cursus feugiat, nunc augue blandit nunc, eu sollicitudin urna dolor sagittis lacus. Donec elit libero, sodales nec, volutpat a, suscipit non, turpis. Nullam sagittis. Suspendisse pulvinar, augue ac venenatis condimentum, sem libero volutpat nibh, nec pellentesque velit pede quis nunc. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Fusce id purus.Ut varius tincidunt libero.Phasellus dolor.Maecenas vestibulum mollis diam.Pellentesque ut neque.Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.In dui magna, posuere eget, vestibulum et, tempor auctor, justo. In ac felis quis tortor malesuada pretium.Pellentesque auctor neque nec urna.Proin sapien ipsum, porta a, auctor quis, euismod ut, mi.Aenean viverra rhoncus pede.Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Phasellus accumsan cursus velit.";
  Back_to_Login: string = "Back to Login";

  // Contact Support
  Contact_Us: string = "Contact Us";
  Contact: string = "(555)555-1234";
  Email_Id: string = "<EMAIL>";

  // verified email
  // Your_email_is_Verified: string = "Your email is Verified";
  // Your_email_id_is_successfully_verified: string = "Your registration has been confirmed!";
  // "Your email id is successfully verified";
  // Now_you_can_Login: string = "Please return to the CNA365 login page to enter your email address and password."
  // "Now you can Login";
  // Continue_to_Login: string = " Continue to Login";
  // Your_email_is_already_verified_Continue_Login: string = "Your email is already verified, please click on Continue to Login button";

  // forget
  Enter_your_MailID_to_recieve_resetlink: string =
    "Enter your Mail ID to receive a reset link";
  Submit: string = "Submit";
  Back_to_Sign_In: string = "Back to Sign In";

  // reset
  Reset_Password: string = "Reset Password?";
  Reset_Password_Link_Sent: string =
    "Reset Password Link has been sent to your Mail";
  Access_code: string = "Access code";
  access_code_placeholder: string = "253436";
  email_placeholder: string = "<EMAIL>";
  pwd_placeholder: string = "********";
  Back: string = "Back";
  Reset_Password_btn: string = "Reset Password";
  New_Password: string = "New Password";
  // reset form validation msg's
  access_code_is_required: string = "Access code is required";
  // reset form snackbar msg's
  Password_updated_successfully: string = "Password updated successfully";
  Please_try_again: string = "Please try again";
  // terms and conditions
  Terms_and_conditions: string = "Terms and conditions";
  Last_Updated_Date: string = "Last Updated June 24, 2021";
  Summary: string = "Summary:";
  General_Terms_Conditions: string = "General Terms and Conditions";
  Acceptable_UsePolicy: string = "Acceptable Use Policy";
  Cancellation_Policy: string = "Cancellation Policy";
  Privacy_Policy: string = "Privacy Policy";

  // forgot pwd
  Please_check_your_mail_for_accesscode: string =
    "Please check your mail for the access code";
  Invalid_emailID: string = "Invalid emailID";

  // Change pwd
  Change_Password: string = "Change Password";
  Old_Password: string = "Old Password";
  Password_changed_successfully: string = "Password changed successfully";

  //Select two factor auth type
  Two_Factor_Authentication_Required: string =
    "Two Factor Authentication Required";
  Please_select_your_preferred_two_factor_authentication_method: string =
    "Please select your preferred two factor authentication method";
  SMS: string = "SMS";
  Text_Message: string = "Text Message";

  // Confirm MFA
  Please_confirm_your_code: string = "Please confirm your code";
  Confirm_code: string = "Confirm code";
  Verification_Code = "Verification Code";
  Verification_Code_Message: string =
    "Please enter with the verification code sent to your phone number or email";
  Code_is_required: string = "Code is required";
  Please_enter_valid_code: string = "Please enter valid code";
  Code_expired_please_sign_in_again: string =
    "Code expired. Please sign in again.";
  Invalid_Code_please_try_again: string = "Invalid Code. Please try again";

  passEv($event) {
    // debugger;
    this.resetPasswordTest = $event.target.value.toLowerCase();
    if (this.resetPasswordTest && "test") {
      console.log(this.paaswordProductName);
      this.namePartRegex = "test"
        .split(" ")
        .map((part) => (part.length >= 4 ? part.substring(0, 4) : ""))
        .join("|");

      const regex = new RegExp(this.namePartRegex, "i");
      if (regex.test(this.resetPasswordTest)) {
        return (this.paaswordProductName = true);
      } else if (this.resetPasswordTest && "examroom") {
        this.namePartRegex = "examroom"
          .split(" ")
          .map((part) => (part.length >= 8 ? part.substring(0, 8) : ""))
          .join("|");

        const regex = new RegExp(this.namePartRegex, "i");
        if (regex.test(this.resetPasswordTest)) {
          return (this.paaswordProductName = true);
        } else if (this.resetPasswordTest && "credentia") {
          this.namePartRegex = "credentia"
            .split(" ")
            .map((part) => (part.length >= 9 ? part.substring(0, 9) : ""))
            .join("|");

          const regex = new RegExp(this.namePartRegex, "i");
          if (regex.test(this.resetPasswordTest)) {
            return (this.paaswordProductName = true);
          } else if (this.resetPasswordTest && "gyroscope") {
            this.namePartRegex = "Gyroscope"
              .split(" ")
              .map((part) => (part.length >= 9 ? part.substring(0, 9) : ""))
              .join("|");

            const regex = new RegExp(this.namePartRegex, "i");
            if (regex.test(this.resetPasswordTest)) {
              return (this.paaswordProductName = true);
            } else if (this.resetPasswordTest && "itembank") {
              this.namePartRegex = "itembank"
                .split(" ")
                .map((part) => (part.length >= 8 ? part.substring(0, 8) : ""))
                .join("|");

              const regex = new RegExp(this.namePartRegex, "i");
              if (regex.test(this.resetPasswordTest)) {
                return (this.paaswordProductName = true);
              } else if (this.resetPasswordTest && "Valhalla") {
                this.namePartRegex = "Valhalla"
                  .split(" ")
                  .map((part) => (part.length >= 8 ? part.substring(0, 8) : ""))
                  .join("|");

                const regex = new RegExp(this.namePartRegex, "i");
                if (regex.test(this.resetPasswordTest)) {
                  return (this.paaswordProductName = true);
                } else if (this.resetPasswordTest && "NIC") {
                  this.namePartRegex = "NIC"
                    .split(" ")
                    .map((part) =>
                      part.length >= 3 ? part.substring(0, 3) : ""
                    )
                    .join("|");

                  const regex = new RegExp(this.namePartRegex, "i");
                  if (regex.test(this.resetPasswordTest)) {
                    return (this.paaswordProductName = true);
                  } else if (this.resetPasswordTest && "Standard Setting") {
                    this.namePartRegex = "StandardSetting"
                      .split(" ")
                      .map((part) =>
                        part.length >= 15 ? part.substring(0, 15) : ""
                      )
                      .join("|");

                    const regex = new RegExp(this.namePartRegex, "i");
                    if (regex.test(this.resetPasswordTest)) {
                      return (this.paaswordProductName = true);
                    } else if (
                      this.resetPasswordTest &&
                      " Test Site Managment"
                    ) {
                      this.namePartRegex = "TestSiteManagment"
                        .split(" ")
                        .map((part) =>
                          part.length >= 17 ? part.substring(0, 17) : ""
                        )
                        .join("|");

                      const regex = new RegExp(this.namePartRegex, "i");
                      if (regex.test(this.resetPasswordTest)) {
                        return (this.paaswordProductName = true);
                      } else if (this.resetPasswordTest && "NCSBN") {
                        this.namePartRegex = "NCSBN"
                          .split(" ")
                          .map((part) =>
                            part.length >= 5 ? part.substring(0, 5) : ""
                          )
                          .join("|");

                        const regex = new RegExp(this.namePartRegex, "i");
                        if (regex.test(this.resetPasswordTest)) {
                          return (this.paaswordProductName = true);
                        } else if (this.resetPasswordTest && "SAMPLE") {
                          this.namePartRegex = "SAMPLE"
                            .split(" ")
                            .map((part) =>
                              part.length >= 6 ? part.substring(0, 6) : ""
                            )
                            .join("|");

                          const regex = new RegExp(this.namePartRegex, "i");
                          if (regex.test(this.resetPasswordTest)) {
                            return (this.paaswordProductName = true);
                          } else if (this.resetPasswordTest && "DEMO") {
                            this.namePartRegex = "DEMO"
                              .split(" ")
                              .map((part) =>
                                part.length >= 4 ? part.substring(0, 4) : ""
                              )
                              .join("|");

                            const regex = new RegExp(this.namePartRegex, "i");
                            if (regex.test(this.resetPasswordTest)) {
                              return (this.paaswordProductName = true);
                            } else if (this.resetPasswordTest && "PROVEXAM") {
                              this.namePartRegex = "PROVEXAM"
                                .split(" ")
                                .map((part) =>
                                  part.length >= 8 ? part.substring(0, 8) : ""
                                )
                                .join("|");

                              const regex = new RegExp(this.namePartRegex, "i");
                              if (regex.test(this.resetPasswordTest)) {
                                return (this.paaswordProductName = true);
                              } else if (this.resetPasswordTest && "CNA") {
                                this.namePartRegex = "CNA"
                                  .split(" ")
                                  .map((part) =>
                                    part.length >= 3 ? part.substring(0, 3) : ""
                                  )
                                  .join("|");

                                const regex = new RegExp(
                                  this.namePartRegex,
                                  "i"
                                );
                                if (regex.test(this.resetPasswordTest)) {
                                  return (this.paaswordProductName = true);
                                } else if (this.resetPasswordTest && "Exam") {
                                  this.namePartRegex = "Exam"
                                    .split(" ")
                                    .map((part) =>
                                      part.length >= 4
                                        ? part.substring(0, 4)
                                        : ""
                                    )
                                    .join("|");

                                  const regex = new RegExp(
                                    this.namePartRegex,
                                    "i"
                                  );
                                  if (regex.test(this.resetPasswordTest)) {
                                    return (this.paaswordProductName = true);
                                  } else if (
                                    this.resetPasswordTest &&
                                    "Edison"
                                  ) {
                                    this.namePartRegex = "Edison"
                                      .split(" ")
                                      .map((part) =>
                                        part.length >= 6
                                          ? part.substring(0, 6)
                                          : ""
                                      )
                                      .join("|");

                                    const regex = new RegExp(
                                      this.namePartRegex,
                                      "i"
                                    );
                                    if (regex.test(this.resetPasswordTest)) {
                                      return (this.paaswordProductName = true);
                                    } else if (
                                      this.resetPasswordTest &&
                                      "NNAAP"
                                    ) {
                                      this.namePartRegex = "NNAAP"
                                        .split(" ")
                                        .map((part) =>
                                          part.length >= 5
                                            ? part.substring(0, 5)
                                            : ""
                                        )
                                        .join("|");

                                      const regex = new RegExp(
                                        this.namePartRegex,
                                        "i"
                                      );
                                      if (regex.test(this.resetPasswordTest)) {
                                        return (this.paaswordProductName =
                                          true);
                                      } else if (
                                        this.resetPasswordTest &&
                                        "nclex"
                                      ) {
                                        this.namePartRegex = "nclex"
                                          .split(" ")
                                          .map((part) =>
                                            part.length >= 5
                                              ? part.substring(0, 5)
                                              : ""
                                          )
                                          .join("|");

                                        const regex = new RegExp(
                                          this.namePartRegex,
                                          "i"
                                        );
                                        if (
                                          regex.test(this.resetPasswordTest)
                                        ) {
                                          return (this.paaswordProductName =
                                            true);
                                        } else if (
                                          this.resetPasswordTest &&
                                          "password"
                                        ) {
                                          this.namePartRegex = "password"
                                            .split(" ")
                                            .map((part) =>
                                              part.length >= 8
                                                ? part.substring(0, 8)
                                                : ""
                                            )
                                            .join("|");

                                          const regex = new RegExp(
                                            this.namePartRegex,
                                            "i"
                                          );
                                          if (
                                            regex.test(this.resetPasswordTest)
                                          ) {
                                            return (this.paaswordProductName =
                                              true);
                                          } else {
                                            return (this.paaswordProductName =
                                              false);
                                          }
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    } else {
      return (this.paaswordProductName = false);
    }
  }

  passTextChecking($event) {
    this.passTextevent = $event.target.value;
    if (this.firstNameevent && this.passTextevent) {
      const namePartRegex = this.createNamePartRegex(
        this.firstNameevent,
        this.firstNameevent.length
      );
      if (this.checkPasswordAgainstRegex(namePartRegex)) {
        this.passwordContainsFullNamePartnew = true;
        return;
      } else {
        this.passwordContainsFullNamePartnew = false;
      }
    }

    if (this.middleNameevent && this.passTextevent) {
      const namePartRegex = this.createNamePartRegex(
        this.middleNameevent,
        this.middleNameevent.length
      );
      if (!namePartRegex) {
        this.isMiddlestring = true;
      } else if (
        this.checkPasswordAgainstRegex(namePartRegex) &&
        !this.isMiddlestring
      ) {
        this.passwordContainsFullNamePartnew = true;
        return;
      } else {
        this.passwordContainsFullNamePartnew = false;
      }
    }

    if (this.lastNameevent && this.passTextevent) {
      const namePartRegex = this.createNamePartRegex(
        this.lastNameevent,
        this.lastNameevent.length
      );
      if (!namePartRegex) {
        this.isLaststring = true;
      } else if (
        this.checkPasswordAgainstRegex(namePartRegex) &&
        !this.isLaststring
      ) {
        this.passwordContainsFullNamePartnew = true;
      }
    } else {
      this.passwordContainsFullNamePartnew = false;
    }
  }

  createNamePartRegex(name: string, minLength: number): RegExp | null {
    const nameParts = name
      .split(" ")
      .map((part) =>
        part.length >= minLength ? part.substring(0, minLength) : ""
      );
    const namePartRegex = nameParts.join("|");

    return namePartRegex ? new RegExp(namePartRegex, "i") : null;
  }

  checkPasswordAgainstRegex(regex: RegExp): boolean {
    return regex?.test(this.passTextevent);
  }
}
