import { BrowserModule } from '@angular/platform-browser';
import { NgModule } from '@angular/core';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { exaiModule } from '../@exai/exai.module';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { AuthorizationModule } from './authorization/authorization.module';
import { LocationStrategy, HashLocationStrategy, PathLocationStrategy } from '@angular/common';
import { BufferInterceptor } from './buffer-service.service';
import { Store } from '@ngrx/store';
import { StoreModule } from '@ngrx/store';

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    HttpClientModule,
    exaiModule,
    AuthorizationModule,
    StoreModule.forRoot({}),
  ],
  providers: [
    { provide: HTTP_INTERCEPTORS, useClass: BufferInterceptor, multi: true },
    { provide: LocationStrategy, useClass: PathLocationStrategy },
    Store
  ],
  bootstrap: [AppComponent],
})
export class AppModule { }
