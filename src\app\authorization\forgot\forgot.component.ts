import { Component, OnInit, HostListener } from "@angular/core";
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  Validators,
} from "@angular/forms";
import { HttpService } from "src/app/http.service";
import { Router } from "@angular/router";
import { GlobalService } from "src/app/global.service";
import { AuthorizationService } from "../authorization.service";
import { Observable } from "rxjs";
import { getLoading } from "src/app/state/shared/shared.selectors";
import { Store } from "@ngrx/store";
@Component({
  selector: "exai-forgot",
  templateUrl: "./forgot.component.html",
  styleUrls: ["./forgot.component.scss"],
})
export class ForgotComponent implements OnInit {
  form: FormGroup;
  email: AbstractControl;
  btnEnabled: any;
  submitBtnClicked: boolean = false;
  validation_messages = {
    email: [
      { type: "required", message: this.global.Email_is_required },
      { type: "pattern", message: this.global.Enter_a_valid_email },
    ],
  };

  constructor(
    private fb: FormBuilder,
    private services: HttpService,
    private router: Router,
    public global: GlobalService,
    private _authorizationService: AuthorizationService,
    private store: Store
  ) {}

  loadingObs: Observable<boolean>;

  redirectToCredentialTicketForm() {
    window.location.href = "assets/img/demo/credentia-ticket-form.html";
  }

  ngOnInit() {
    this.loadingObs = this.store.select(getLoading);

    this.form = this.fb.group({
      email: [
        "",
        Validators.compose([
          Validators.required,
          Validators.pattern(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,10}$"
          ),
        ]),
      ],
    });

    this.email = this.form.controls["email"];
  }

  @HostListener("window:keydown", ["$event"])
  keyDownFunction(event) {
    if (event.keyCode === 13) {
      this.btnEnabled = document.getElementsByClassName("loginBtn");
      for (let key in this.btnEnabled) {
        if (this.btnEnabled[key].disabled === false) {
          this.forgotPwd();
        }
      }
    }
  }

  forgotPwd(): void {
    this.submitBtnClicked = true;
    this._authorizationService.enableResetFlag = false;

    const inputDetails = { username: this.form.value.email };
    this.services.forgotPwd(inputDetails).subscribe({
      next: (data) => {
        if (data["forgotPassword"].httpStatusCode === 200) {
          this._authorizationService.passwordInformation.email =
            inputDetails.username;
          this.services.callSnackbaronSuccess(
            this.global.Reset_Password_Link_Sent
          );
          // Disable the button to prevent re-submission
          this.form.disable();
        } else {
          this.services.callSnackbaronError(this.global.Invalid_emailID);
          this.submitBtnClicked = false;
        }
      },
      error: (err) => {
        this.services.callSnackbaronError(err.error);
        this.submitBtnClicked = false;
      },
    });
  }
}
