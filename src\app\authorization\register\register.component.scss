.cardHeight {
    height: calc(100vh - var(--toolbar-height) - 80px);
    margin-bottom: 30px;
    overflow-y: auto;
    overflow-x: hidden;
}

.bg-pattern {
    background: var(--page_background);
    height: 100%;
    overflow: auto;
}

.bottomSpace {
    padding-bottom: var(--toolbar-height);
}

.card {
    border: 0.3px solid var(--card-border);
    border-radius: 4px;
}

h3 {
    color: var(--primary);
}

.loginForm {
    padding: 0 2.75rem 2.75rem;
}

.subheading-1 {
    font-weight: 600;
    font-size: 14px;
}

.mat-icon{
    color: rgba(var(--color-warn), 1);
    font-size: 12px
}


.contentdone{
    margin-left: -25px;
    font-size: 0.75rem;
    color: var(--primary);
    margin-top: 10px;
}

.done{
    color: var(--primary);
    font-size: 12px
}
.close{
    color: rgba(var(--color-warn), 1);
    font-size: 0.65rem;
   
}

::ng-deep :root .text-xxs {
    font-size: 0.70rem!important
}


::ng-deep .mat-checkbox-inner-container{
    width: 12px !important;
    height: 12px !important;
}

.terms {
    font-size: 10px;
    color: var(--card-border);
    a {
        color: var(--primary);
        cursor: pointer;
    }
}

.loginBtn {
    background-color: var(--primary);
    font-weight: 500;
    font-size: 14px;
    margin-top: 1.2rem;
    padding-right: 100px;
    padding-left: 100px;
    &:hover:enabled {
        background-color: var(--btn-hover);
    }
    &:active:enabled {
        background-color: var(--btn-pressed);
    }
}

.haveAccount {
    font-size: 12px;
    color: var(--card-border);
    a {
        color: var(--primary);
    }
}

.flagLogo {
    margin-right: 0px !important;
    margin-left: auto !important;
}

.lockOutline {
    margin-left: 6px;
    font-size: 15px;
    margin-bottom: 3px;
    vertical-align: middle;
}

.v-center {
    display: flex;
    align-items: center;
    height: 100%;
    width: 100%;
    position: absolute;
    background: #e5e5e5;
    > .config {
        width: 720px;
        margin: 0 auto;
        text-align: center;
        border: 0.3px solid #7d7d7d;
        border-radius: 4px;
        padding: 45px;
        background: #ffffff;
    }
    .example-full-width {
        width: 100%;
    }
    h1 {
        color: var(--primary);
        font-size: 20px;
        font-weight: 500;
        mat-icon {
            font-weight: 500;
            font-size: 20px;
            color: var(--primary);
            vertical-align: middle;
        }
    }
    h2 {
        float: left;
        color: #11263c;
        font-weight: 500;
        font-size: 16px;
        margin: 11px 0 8px 0px;
    }
}

.title-p {
    color: var(--forgot-password-heading);
    font-size: 12px;
    text-align: center;
}

.loginFormLogo {
    padding-bottom: 0.5rem;
    color: var(--primary);
}

.emailVerfnDesc {
    font-size: 12px;
    color: var(--card-border);
    text-align: center;
}

.emailImg {
    max-width: 15rem;
}

.incorrectEmail {
    font-size: 12px;
    color: var(--primary);
    cursor: pointer;
    &:hover {
        color: var(--btn-hover);
    }
    &:active {
        color: var(--btn-pressed);
    }
}

.strokedBtn {
    &:hover:enabled {
        border-color: var(--verifyEmailBtns-onhover);
        color: var(--verifyEmailBtns-onhover);
    }
    &:active:enabled {
        border-color: var(--verifyEmailBtns-onactive);
        color: var(--btn-pressed);
    }
}

.example-button-row {
    display: table-cell;
    width: 490px;
}

.subHeading {
    padding-top: 0.75rem;
}

// Date-of-birth input color change
.mat-input-element:disabled,
.mat-form-field-type-mat-native-select.mat-form-field-disabled .mat-form-field-infix::after {
    color: var(--forgot-password-heading);
}

// Phone number input with country flags list
:host {
    ::ng-deep {
        input.mat-input-element {
            line-height: 0;
        }
        .country-selector {
            width: 0px !important;
            text-align: start;
            opacity: 1 !important;
        }
        .mat-button {
            min-width: 77px !important;
        }
        .phNumberInput.mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float
            .mat-form-field-outline-gap {
            border-top-color: currentColor;
        }
        .phNumberInput .mat-form-field-label-wrapper {
            margin-left: 80px;
        }
        .phNumberInput .mat-form-field-label {
            transition: none;
        }
        .phNumberInput.mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float
            .mat-form-field-label,
        .phNumberInput.mat-form-field-appearance-outline.mat-form-field-can-float
            .mat-input-server:focus
            + .mat-form-field-label-wrapper
            .mat-form-field-label {
            transform: none;
            width: 0;
        }
        .phNumberInput.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label,
        .phNumberInput.mat-form-field-can-float
            .mat-input-server:focus
            + .mat-form-field-label-wrapper
            .mat-form-field-label {
            transform: none;
            width: 0;
        }
    }
}

// loader
.card-div {
    position: inherit;
    margin-right: auto !important;
    margin-left: auto !important;
}
.spinner {
    text-align: center;
}
.spinner > div {
    width: 18px;
    height: 18px;
    background-color: var(--primary);
    border-radius: 100%;
    display: inline-block;
    -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
    animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}
.spinner .bounce1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}
.spinner .bounce2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
}
@-webkit-keyframes sk-bouncedelay {
    0%,
    80%,
    100% {
        -webkit-transform: scale(0);
    }
    40% {
        -webkit-transform: scale(1);
    }
}
@keyframes sk-bouncedelay {
    0%,
    80%,
    100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    }
    40% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}

// Tooltip for Password

ul{
    list-style-type: disc;
}

::ng-deep{
    .tooltip {
        text-align: inherit !important;
    }
}
h6{
   text-decoration: seagreen;
}
.text-size{
    font-size: 12px;
}

.cardBorder {
    border-bottom: 1px solid var(--card-border);
    background-color: #f9f9f9;
}

.note {
    // font-style: italic;
    font-size: 0.65rem!important;
}

.state-name {
    color: #f3325c;
    font-size: 1.25rem!important;
    font-weight: 600;
}


.color1{
    color:blue;
}
.textsize{
    font-size:  0.7em;
}
::ng-deep .Border {
        color: #f44336 !important;

}
::ng-deep .Border1 {
    color:var(--primary) !important;

}

.error_font_size{
    font-size: 0.58rem;
    color:rgba(244, 67, 54, 1);
    line-height: 10px;
    //margin-top: 0.5rem;
}
.ButtonChat{
    color: #1c75bc;
    // background-color: #1c75bc;
  }
  .chat{
    display: flex;
    flex-direction: row-reverse;
    padding-right: 2%;
    padding-top: 1%;
    background-color: #f9f9f9;
  }
.flagLogo{
    flex-direction: row !important;
    align-items: initial;
  }
  .w-5{
    width: 1.25rem;
    padding-bottom: 11px;
  }
  .email{
    color: #1c75bc !important;
font-size: 1.50rem !important;
  }
  

