import {Component, OnInit, HostListener} from '@angular/core';
import {AbstractControl, FormBuilder, FormGroup, Validators} from '@angular/forms';
import {HttpService} from 'src/app/http.service';
import {Router} from "@angular/router";
import {GlobalService} from 'src/app/global.service';
import {AuthorizationService} from '../authorization.service';
import {Observable} from 'rxjs';
import {getLoading} from 'src/app/state/shared/shared.selectors';
import {Store} from '@ngrx/store';
import {environment} from 'src/environments/environment';

@Component({
    selector: 'exai-confirm-mfa',
    templateUrl: './confirm-mfa.component.html',
    styleUrls: ['./confirm-mfa.component.scss']
})
export class ConfirmMfaComponent implements OnInit {

    form: FormGroup;
    confirmationCode: AbstractControl;
    btnEnabled: any;
    submitBtnClicked: boolean = false;

    constructor(public global: GlobalService, private fb: FormBuilder,
                private services: HttpService, private _router: Router, private _authorizationService: AuthorizationService, private _store: Store) {
        const info = _authorizationService.tempPasswordInfo;

        if (!(info && 'email' in info && 'password' in info)) {
            this._router.navigateByUrl('');
        }

        this.email = info.email;
        this.session = info.session;
        this.password = info?.password;
    }

    email = '';
    session = '';
    password = '';

    validation_messages = {
        'confirmationCode': [
            {type: 'required', message: this.global.Code_is_required},
            {type: 'pattern', message: this.global.Please_enter_valid_code}
        ]
    };

    loadingObs: Observable<boolean>;

    ngOnInit() {

        this.loadingObs = this._store.select(getLoading)

        this.form = this.fb.group({
            confirmationCode: ['', Validators.compose([Validators.required,
                Validators.pattern('^[a-f0-9]{6}$')])],
        });

        this.confirmationCode = this.form.controls['confirmationCode'];
    }

    @HostListener('window:keydown', ['$event'])
    keyDownFunction(event) {
        if (event.keyCode === 13) {
            this.btnEnabled = document.getElementsByClassName('loginBtn');
            for (let key in this.btnEnabled) {
                if (this.btnEnabled[key].disabled === false) {
                    this.confirmMFA();
                }
            }
        }
    }

    confirmMFA(): void {
        this.submitBtnClicked = true;
        const inputDetails = {
            username: this.email,
            session: this.session,
            confirmationCode: this.form.value.confirmationCode
        };
        this.services.confirmMFA(inputDetails).subscribe((data: any) => {
            if (data) {
                if(data.stateId==this.global.PAStateId && this._authorizationService.tempPasswordInfo.password.length<12 && data.challengeName=="Success"){
                    this._authorizationService.enableResetFlag=true;
                    this._authorizationService.accessToken=data.accessToken
                    this._router.navigateByUrl('/reset')
                  }
                else if (data.challengeName == "NEW_PASSWORD_REQUIRED") {
                    this._authorizationService.tempPasswordInfo.email = this.email;
                    this._authorizationService.tempPasswordInfo.tempPwd = this.password;
                    this.services.callSnackbaronSuccess(this.global.Please_change_your_pwd);
                    this._router.navigateByUrl('changePassword');
                } else if (data.challengeName == "CUSTOM_CHALLENGE") {
                    this.services.callSnackbaronError(this.global.Invalid_Code_please_try_again);
                    this.session = data.session;
                    this.submitBtnClicked = false;
                } else {
                    this.SelectRole(data);
                }
            } else {
                this.services.callSnackbaronError("error");
                this.submitBtnClicked = false;
            }
        }, err => {
            if (err.error === "Invalid session for the user.") {
                this.services.callSnackbaronError(this.global.Code_expired_please_sign_in_again);
                this._router.navigateByUrl('')
            } else {
                this.services.callSnackbaronError(err.error);
            }
            this.submitBtnClicked = false;
        });
    }

    SelectRole(a) {

        switch (a.roles[0].roleId) {
            case this.global.Candidate_RoleID: {
                window.location.href = environment.redirectUrl + environment.candidate + `/?token=${a.idToken}`;
                break;
            }
            case this.global.SuperAdmin_RoleID: {
                window.location.href = environment.redirectUrl + environment.client + `/?token=${a.idToken}`;
                break;
            }
            case this.global.OperationStaff_RoleID: {
                window.location.href = environment.redirectUrl + environment.client + `/?token=${a.idToken}`;
                break;
            }
            case this.global.StateClient_RoleID: {
                window.location.href = environment.redirectUrl + environment.client + environment.state + `/?token=${a.idToken}`;
                break;
            }
            case this.global.TrainingInstitute_RoleID: {
                window.location.href = environment.redirectUrl + environment.client + environment.training + `/?token=${a.idToken}`;
                break;
            }
            case this.global.Sponser_RoleID: {
                window.location.href = environment.redirectUrl + environment.client + environment.sponsor + `/?token=${a.idToken}`;
                break;
            }
            case this.global.SupportStaff: {
                window.location.href = environment.redirectUrl + environment.client + `/?token=${a.idToken}`;
                break;
            }
            case this.global.Employer: {
                window.location.href = environment.redirectUrl + environment.client + `/?token=${a.idToken}`;
                break;
            }
            default: {
                this.services.callSnackbaronError(this.global.Please_check_your_credentials);
                break;
            }
        }
    }
}
