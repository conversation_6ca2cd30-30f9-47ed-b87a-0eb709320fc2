import {
  Component,
  OnInit,
  HostListener,
  <PERSON>Changes,
  OnChanges,
  Input,
} from "@angular/core";
import {
  <PERSON><PERSON><PERSON>er,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { MatDialog } from "@angular/material/dialog";
import { HttpService } from "../../http.service";
import { TermsDialogComponent } from "./terms-dialog/terms-dialog.component";
import { DatePipe } from "@angular/common";
import { GlobalService } from "../../global.service";
import { ActivatedRoute } from "@angular/router";
import { Observable } from "rxjs";
import { getLoading } from "src/app/state/shared/shared.selectors";
import { Store } from "@ngrx/store";
import { SupportComponent } from "./support/support.component";
import { HttpClient } from "@angular/common/http";
import { environment } from "src/environments/environment";
import { DateFilterDirective } from "../register/date-filter.directive";
@Component({
  selector: "exai-register",
  templateUrl: "./register.component.html",
  styleUrls: ["./register.component.scss"],
})
export class RegisterComponent implements OnInit {
  ShowSignUp: boolean = false;
  // firstNameevent: any;
  // lastNameevent: any;
  // middleNameevent: any;
  passTextevent: any;
  passwordContainsFullNamePartnew;
  passwordTest: string;
  fullUserName: string;
  isMiddlestring: boolean = false;
  resetPasswordTest;
  namePartRegex;
  paaswordProductName;
  CanMirate: boolean;
  form: FormGroup;
  registrationCompleted: boolean = false;
  serviceType: string = "regular";

  genders: Array<string> = [];
  organizations = ["CD"];
  states = [];
  stateCode: string;
  certNumber: number | string;
  isChecked: any = false;
  roleId: number;
  orgIDValue: any;
  showorgIDField: boolean = true;
  showtermsPopup: boolean = false;
  showregisterfield: boolean = false;
  emailID: any;
  dialogHeight: any;
  Date_of_Birth: Date | string;
  currentdate: string;
  today = new Date();
  ageRestriction: number;
  ageLimit: number;
  clientStateCode: string = "";
  maxDate: Date;
  dateFilter: (date: Date) => boolean;
  public isUserVerifiedLink: boolean = false;
  CheckValueNameorProduct: string;
  btnEnabled: any;
  roleIDfromParams: number;
  signUpBtnClicked: boolean = false;
  ErrorMessage: string = " ";
  //dobInvalid: boolean = false
  // isStateCodePA: boolean = false;
  isSponsor: boolean = false;
  StateCode: string;
  // stateCodefromQueryParams: any;
  // defaultStateName: any;
  roleIdfromQueryParamstoSignInPage;
  tenantCodefromQueryParamstoSignInPage;
  stateCodefromQueryParamstoSignInPage;
  // statesCodeforssnvaln: Array<string> = [];
  textNameAsGovtId: boolean = true;
  errors: string = null;
  // maxDate: Date;
  validation_messages = {
    firstName: [
      { type: "required", message: this.global.First_name_is_required },
      {
        type: "maxlength",
        message: this.global.Maximum_50_characters_are_allowed,
      },
      { type: "pattern", message: this.global.Please_enter_valid_first_name },
    ],
    middleName: [],
    lastName: [
      { type: "required", message: this.global.Last_name_is_required },
      {
        type: "maxlength",
        message: this.global.Maximum_50_characters_are_allowed,
      },
      { type: "pattern", message: this.global.Please_enter_valid_last_name },
    ],
    orgID: [
      {
        type: "required",
        message: this.global.Please_enter_your_organization_ID,
      },
    ],
    dob: [{ type: "required", message: this.global.Please_select_your_DOB }],
    gender: [
      { type: "required", message: this.global.Please_select_your_gender },
    ],
    address: [
      { type: "required", message: this.global.Please_enter_your_address },
      {
        type: "maxlength",
        message: this.global.Maximum_225_characters_are_allowed,
      },
    ],
    city: [
      { type: "required", message: this.global.Please_enter_your_city },
      {
        type: "maxlength",
        message: this.global.Maximum_25_characters_are_allowed,
      },
      { type: "pattern", message: this.global.Please_enter_valid_City_name },
    ],
    zipCode: [
      { type: "required", message: this.global.Zip_code_is_required },
      { type: "pattern", message: this.global.Maximum_5_numbers_are_allowed },
    ],
    state: [
      { type: "required", message: this.global.Please_enter_your_state },
      {
        type: "maxlength",
        message: this.global.Maximum_25_characters_are_allowed,
      },
      { type: "pattern", message: this.global.Please_enter_valid_state_name },
    ],
    ssn: [
      {
        type: "required",
        message: this.global.Please_enter_your_Social_Security_number,
      },
      { type: "pattern", message: this.global.Please_enter_valid_SSN },
    ],
    ssnColorado: [
      { type: "required", message: this.global.Please_enter_your_SSN_or_ITIN },
      { type: "pattern", message: this.global.Please_enter_valid_SSN_or_ITIN },
    ],
    phoneNumber: [
      { type: "required", message: this.global.Phone_number_is_required },
      { type: "minlength", message: this.global.Enter_a_valid_phone_number },
      { type: "maxlength", message: this.global.Enter_a_valid_phone_number },
    ],
    _email: [
      { type: "required", message: this.global.Email_is_required },
      { type: "pattern", message: this.global.Enter_a_valid_email },
    ],
    password: [
      { type: "required", message: this.global.Password_is_required },
      {
        type: "maxlength",
        message: this.global.Password_should_not_exceed_99_characters,
      },
      { type: "pattern", message: this.global.Use_12_or_more_characters },
    ],
  };

  constructor(
    private formBuilder: FormBuilder,
    private dialog: MatDialog,
    private _route: ActivatedRoute,
    private services: HttpService,
    public datepipe: DatePipe,
    public global: GlobalService,
    private route: ActivatedRoute,
    private store: Store,
    private http: HttpClient
  ) {
    this.form = this.formBuilder.group({
      dob: ["", Validators.required],
    });
    this.dateFilter = (date: Date) => this.isDateSelectable(date);
  }

  loadingObs: Observable<boolean>;

  redirectToCredentialTicketForm() {
    window.location.href = "assets/img/demo/credentia-ticket-form.html";
  }
  initializeForm(): void {
    this.form = this.formBuilder.group({
      dob: ["", Validators.required],
    });
  }

  findAgeLimitForState(ageLimits: any[], stateCode: string): number {
    const stateAgeLimit = ageLimits.find(
      (limit) => limit.clientStateCode === stateCode
    );
    return stateAgeLimit ? stateAgeLimit.ageLimit : 0;
  }

  setMinDate(): void {
    const minDate = new Date();
    minDate.setFullYear(
      minDate.getFullYear() - Math.floor(this.ageRestriction)
    );
    minDate.setMonth(
      minDate.getMonth() - Math.floor((this.ageRestriction % 1) * 12)
    );

    const minDateTimestamp = minDate.getTime(); // Convert Date to timestamp

    this.form
      .get("dob")
      .setValidators([Validators.required, Validators.min(minDateTimestamp)]);
    this.form.get("dob").updateValueAndValidity();
  }

  // onDateChange(event: MatDatepickerInputEvent<Date>): void {
  //   // Handle date change if needed
  // }

  ngOnInit() {
    // this.updateClientStateCode();
    this._route.queryParams.subscribe({
      next: (param) => {
        if (param["StateCode"] === "WA") {
          this.genders = ["Male", "Female", "Prefer Not to Say", "X"];
        } else {
          this.genders = ["Male", "Female"];
        }
        this.loadStateAgeLimits(param["StateCode"]);
      },
    });
    this.dateFilter = (date: Date) => this.isDateSelectable(date);

    this.loadingObs = this.store.select(getLoading);

    if (location.search) {
      this.route.queryParams.subscribe((params) => {
        this.roleId = params.RoleId;
        this.StateCode = params.StateCode;
        if (params.TenantCode) {
          this.tenantCodefromQueryParamstoSignInPage = params.TenantCode;
          this.orgIDValue = params.TenantCode;
          if (params.TenantCode) {
            this.showorgIDField = false;
          }

          if (this.StateCode == "PA") {
            this.showregisterfield = true;
          }
        }

        if (params.RoleId) {
          this.roleIdfromQueryParamstoSignInPage = params.RoleId;
        }

        if (params.StateCode) {
          this.stateCodefromQueryParamstoSignInPage = params.StateCode;
        }

        this.clientStateCode = params.StateCode;
        this.roleIDfromParams = +params.RoleId;

        // if (params.StateCode && params.StateCode == "PA") {
        //   this.isStateCodePA = true;
        // }
        if (Number(params.RoleId) == this.global.Sponser_RoleID) {
          this.isSponsor = true;
        }
        // if (params.StateCode) {
        //   this.stateCodefromQueryParams = params.StateCode;
        // }
      });
    }

    this.services.getStates().subscribe(
      (data: any) => {
        if (data) {
          for (var index in data) {
            this.states.push({
              stateName: data[index].stateName,
              stateCode: data[index].stateCode,
            });
            // this.statesCodeforssnvaln.push(data[index].stateCode);
            // console.log(this.statesCodeforssnvaln);
          }
          // if (this.stateCodefromQueryParams) {
          //   for (var i = 0; i < this.states.length; i++) {
          //     if (this.stateCodefromQueryParams == this.states[i].stateCode) {
          //       this.defaultStateName = this.states[i].stateName;
          //       break;
          //     }
          //   }
          //   this.onOptionsSelected(this.defaultStateName);
          // }
        }
      },
      (error) => {
        // console.log(error);
        if (error.error && typeof error.error == "string")
          this.services.callSnackbaronError(error.error);
      }
    );

    // const bc = new BroadcastChannel('test_channel');

    this.form = new FormGroup(
      {
        firstName: new FormControl(
          "",
          Validators.compose([
            Validators.required,
            Validators.maxLength(50),
            Validators.pattern("^[a-zA-Z ]+(?:[s-][a-zA-Z]+)*$"),
          ])
        ),
        middleName: new FormControl("", []),
        lastName: new FormControl(
          "",
          Validators.compose([
            Validators.required,
            Validators.maxLength(50),
            Validators.pattern("^[a-zA-Z ]+(?:[s-][a-zA-Z]+)*$"),
          ])
        ),
        orgID: new FormControl([
          { value: this.orgIDValue ? this.orgIDValue : "" },
          Validators.compose([Validators.required]),
        ]),
        dob: new FormControl("", Validators.compose([Validators.required])),
        gender: new FormControl("", Validators.compose([Validators.required])),
        address: new FormControl(
          "",
          Validators.compose([Validators.required, Validators.maxLength(225)])
        ),
        city: new FormControl(
          "",
          Validators.compose([
            Validators.required,
            ,
            Validators.maxLength(25),
            Validators.pattern("^[a-zA-Z -]+(?:[s-][a-zA-Z]+)*$"),
          ])
        ),
        Registrationnumber: new FormControl(""),
        state: new FormControl(
          "",
          Validators.compose([
            Validators.required,
            Validators.maxLength(25),
            Validators.pattern("^[a-zA-Z -]+(?:[s-][a-zA-Z]+)*$"),
          ])
        ),
        zipCode: new FormControl(
          "",
          Validators.compose([
            Validators.required,
            Validators.pattern("^[0-9]{5}(?:-[0-9]{4})?$"),
          ])
        ),
        phoneNumber: new FormControl(
          "",
          Validators.compose([
            Validators.required,
            Validators.minLength(12),
            Validators.maxLength(14),
            Validators.pattern(/^(\+1|1)?(943|[2-9][0-9]{2})[2-9][0-9]{6}$/),
          ])
        ),
        _email: new FormControl(
          "",
          Validators.compose([
            Validators.required,
            Validators.pattern(
              "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,10}$"
            ),
          ])
        ),
        ssn: new FormControl(
          "",
          Validators.compose([
            Validators.required,
            Validators.pattern("^[0-9]{9}(?:-[0-9]{9})?$"),
          ])
        ),
        password: new FormControl(
          "",
          Validators.compose([
            Validators.required,
            Validators.minLength(12),
            Validators.maxLength(99),
            Validators.pattern(
              "^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{12,}$"
            ),
          ])
        ),
        confirmPassword: new FormControl("", []),
        confirmSsn: new FormControl("", []),
      },
      [
        matchingTwoFieldsValidator("password", "confirmPassword"),
        matchingTwoFieldsValidator("ssn", "confirmSsn"),
      ]
    );

    // if (this.isStateCodePA) {
    //   this.form.get('ssn').clearValidators();
    // }

    if (
      this.global.stateCodestoClearSSNValidation.includes(
        this.stateCodefromQueryParamstoSignInPage
      )
    ) {
      // this.form.get('ssn').clearValidators();
      this.form.controls["ssn"].clearValidators();
    }

    if (
      this.global.textInRegisterFormasGovtID.includes(
        this.roleIdfromQueryParamstoSignInPage
      )
    ) {
      this.textNameAsGovtId = false;
    }

    if (this.isSponsor) {
      // this.form.get('ssn').clearValidators();
      // this.form.get('dob').clearValidators();
      // this.form.get('gender').clearValidators();
      this.form.controls["ssn"].clearValidators();
      this.form.controls["dob"].clearValidators();
      this.form.controls["gender"].clearValidators();
    }

    // bc.addEventListener('message', (event) => {
    //   // console.log(event.data);
    //   this.isUserVerifiedLink = event.data == 'true' ? true : false;
    //   if (this.isUserVerifiedLink == false) {
    //     // this.services.callSnackbaronWarning("Please click on Resend Email button to get verification link again");
    //   }
    // });
  }
  loadStateAgeLimits(stateCode: string) {
    this.services.getStateAgeLimits(stateCode).subscribe((data) => {
      if (data && data.length > 0) {
        this.ageLimit = data[0].ageLimit;
        this.calculateMaxDate();
        this.updateCalendarState();
      } else {
        // Handle case where age limit is not available for the current state code
        this.setDefaultCalendarState();
      }
    });
  }

  // calculateMaxDate() {
  //   const today = new Date();
  //   this.maxDate = new Date(today.getFullYear() - this.ageLimit, today.getMonth(), today.getDate());
  // }
  calculateMaxDate() {
    const today = new Date();
    const years = Math.floor(this.ageLimit); // Extract the whole years
    const monthsDecimal = this.ageLimit - years; // Extract the decimal part representing months

    // Calculate months and days
    const months = Math.floor(monthsDecimal * 12);
    const days = Math.floor((monthsDecimal * 12 - months) * 30); // Assuming an average of 30 days per month

    this.maxDate = new Date(
      today.getFullYear() - years,
      today.getMonth() - months,
      today.getDate() - days
    );
  }

  updateCalendarState() {
    const dobControl = this.form.get("dob");
    if (dobControl) {
      dobControl.enable();

      if (this.maxDate) {
        dobControl.setValidators([
          Validators.required,
          Validators.max(this.maxDate.getTime()),
        ]);
      }
    }
  }

  setDefaultCalendarState() {
    // Set a default age limit or any other logic you want when the state code doesn't have an age limit
    this.ageLimit = 0; // Default to 0 for example
    this.calculateMaxDate();
    this.updateCalendarState();
  }
  isDateSelectable(date: Date): boolean {
    if (this.ageLimit === undefined || this.ageLimit === null) {
      // Handle the case where age limit is not available or not yet fetched
      return true;
    }

    const today = new Date();
    const minAgeDate = new Date(
      today.getFullYear() - this.ageLimit,
      today.getMonth(),
      today.getDate()
    );

    // Allow dates that are after the dynamically obtained age limit years ago
    return date > minAgeDate;
  }
  // isDateSelectable(date: Date): boolean {
  //   const today = new Date();
  //   const eighteenYearsAgo = new Date(today.getFullYear() - 18, today.getMonth(), today.getDate());

  //   // Allow dates that are after 18 years ago
  //   return date > eighteenYearsAgo;
  // }

  userMetrics(event) {
    this.certNumber = event;
    if (
      event != "" &&
      event != null &&
      event != undefined &&
      event &&
      this.Date_of_Birth != null &&
      this.Date_of_Birth != "" &&
      this.Date_of_Birth != undefined
    ) {
      this.http
        .get(
          `${environment.apiUrl}validateCertnumber?certnumber=${event}&dateOfBirth=${this.Date_of_Birth}`
        )
        .subscribe(
          (data: any) => {
            if (data) {
              this.CanMirate = data.canMigrate;
              this.errors = ` This registration number is belongs to ${data.firstName} ${data.lastName}. Please Make sure this is your registration number`;
              this.ShowSignUp = true;
            }
          },
          (err: any) => {
            if (
              err.error ==
              "Date of Birth is not matching with the Registration Number."
            ) {
              this.errors = `<p class='text-xxs leading-snug'">Date of Birth is not matching with the Registration Number. </p>`;
              this.ShowSignUp = false;
              this.CanMirate = null;
            } else {
              this.errors = `<p class='text-xxs leading-snug'">No Registration Number Found.<br> Please Enter valid Pennsylvania Registration Number </p>`;
              this.ShowSignUp = false;
              this.CanMirate = null;
            }
          }
        );
    } else {
      this.errors = null;
      this.ShowSignUp = true;
    }
  }

  onDateChange(event: any) {
    // const birthDate = new Date(event.value);
    // const date = new Date();
    // console.log(birthDate.getMonth())
    // console.log(date.getFullYear() - birthDate.getFullYear())
    // if ((date.getFullYear() - birthDate.getFullYear() >= 16) && this.StateCode == "GA" && (date.getMonth() >= birthDate.getMonth()) && (date.getDate() >= birthDate.getDate())) {
    //   this.dobInvalid = false;
    // }
    // else {
    //   this.dobInvalid = true;
    //   this.ErrorMessage = "Your age should be above 16 years"

    // }
    this.Date_of_Birth = this.datepipe.transform(event.value, "yyyy-MM-dd");
    if (
      this.certNumber != null &&
      this.certNumber != undefined &&
      this.certNumber != ""
    ) {
      this.http
        .get(
          `${environment.apiUrl}validateCertnumber?certnumber=${this.certNumber}&dateOfBirth=${this.Date_of_Birth}`
        )
        .subscribe(
          (data: any) => {
            if (data) {
              this.CanMirate = data.canMigrate;
              this.errors = ` This registration number is belongs to ${data.firstName} ${data.lastName}. Please Make sure this is your registration number`;
              this.ShowSignUp = true;
            }
          },
          (err: any) => {
            if (
              err.error ==
              "Date of Birth is not matching with the Registration Number."
            ) {
              this.errors = `<p class='text-xxs leading-snug'">Date of Birth is not matching with the Registration Number. </p>`;
            } else {
              this.errors = `<p class='text-xxs leading-snug'">No Registration Number Found.<br> Please Enter valid Pennsylvania Registration Number </p>`;
            }

            this.ShowSignUp = false;
            this.CanMirate = null;
          }
        );
    } else {
      this.errors = null;
      this.ShowSignUp = true;
      this.CanMirate = null;
      this.certNumber = null;
    }
  }

  showOptions($event) {
    if ($event.checked === true) {
      this.isChecked = true;
    } else {
      this.isChecked = false;
    }
  }

  checkStatus(): boolean {
    const controls = this.form.controls;
    const invalid = [];
    for (const name in controls) {
      invalid.push({ name: name, valid: !controls[name].invalid });
    }
    const status = invalid.every((ele: any) => ele.valid == true);
    return this.isChecked == true ? status : false;
  }

  // Only Integer Numbers for ssn
  keyPressNumbers(event) {
    var charCode = event.which ? event.which : event.keyCode;
    // Only Numbers 0-9
    if (charCode < 48 || charCode > 57) {
      event.preventDefault();
      return false;
    } else {
      this.errors = null;
      return true;
    }
  }

  preventDefault(event: Event) {
    event.preventDefault();
  }

  first($event) {
    this.global.firstNameevent = $event.target.value;
  }

  last($event) {
    this.global.lastNameevent = $event.target.value;
  }
  middle($event) {
    this.global.middleNameevent = $event.target.value;
  }
  passText($event) {
    this.passTextevent = $event.target.value;
    this.global.passTextChecking($event);
    this.passwordContainsFullNamePartnew =
      this.global.passwordContainsFullNamePartnew;
  }
  passEv1($event) {
    this.global.passEv($event);
    this.paaswordProductName = this.global.paaswordProductName;
  }

  checkValueTrue() {
    this.CheckValueNameorProduct =
      this.passwordContainsFullNamePartnew == true
        ? "Password should not contain the firstname or middlename or lastname."
        : this.paaswordProductName == true
        ? "Password should not contain the any product name and word like test."
        : null;
    this.getProductorUserName();
    return this.CheckValueNameorProduct;
  }

  getProductorUserName() {
    let Values = document.querySelectorAll(".mat-form-field-outline-thick");
    let BorderColor =
      Values.length == 15
        ? Values[13]
        : Values.length == 12
        ? Values[10]
        : Values.length == 11
        ? Values[9]
        : Values[12];
    this.CheckValueNameorProduct != null && this.CheckValueNameorProduct != ""
      ? BorderColor?.classList.add("Border")
      : BorderColor?.classList.remove("Border");
  }

  openDialog() {
    this.dialog.open(TermsDialogComponent, {
      data: this.roleId,
      disableClose: false,
      width: "950px",
      height: "500px",
      panelClass: "my-custom-dialog-class",
    });
  }

  openDialogforContactSupport() {
    this.dialog.open(SupportComponent, {
      disableClose: false,
      width: "350px",
      panelClass: "my-custom-dialog-class1",
    });
  }

  @HostListener("window:keydown", ["$event"])
  keyDownFunction(event) {
    if (event.keyCode === 13) {
      this.btnEnabled = document.getElementsByClassName("loginBtn");
      for (let key in this.btnEnabled) {
        if (this.btnEnabled[key].disabled === false) {
          this.register();
        }
      }
    }
  }

  onOptionsSelected(value: string) {
    for (var index in this.states) {
      if (this.states[index].stateName == value) {
        this.stateCode = this.states[index].stateCode;
      }
    }
  }

  getValidate(data) {}

  register() {
    this.signUpBtnClicked = true;
    const phoneNumber = this.form.value.phoneNumber;
    const countryCodeLength = phoneNumber.length - 10;
    const countryCode = phoneNumber.substr(0, countryCodeLength);

    let dob = this.datepipe.transform(this.form.value.dob, "yyyy-MM-dd");
    this.emailID = this.form.value._email;

    let userDetails = {
      id: this.services.id,
      email: this.form.value._email,
      password: this.form.value.confirmPassword,
      firstName: this.form.value.firstName,
      middleName: this.form.value.middleName,
      lastName: this.form.value.lastName,
      ssn: this.form.value.ssn,
      countryCode: countryCode,
      country: "",
      stateCode: this.stateCode,
      stateName: this.form.value.state,
      clientStateCode: this.clientStateCode,
      city: this.form.value.city,
      zipCode: this.form.value.zipCode,
      accommodation: "",
      clientCandidateId: "",
      phoneNumber: this.form.value.phoneNumber,
      tenantCode: this.orgIDValue ? this.orgIDValue : this.form.value.orgID,
      tenantId: this.services.tenantId,
      createdBy: this.services.createdBy,
      address: this.form.value.address,
      dateofBirth: dob,
      gender: this.form.value.gender,
      role: [
        this.roleIDfromParams ? this.roleIDfromParams : this.services.roleID,
      ],
      registryNumber: this.form.value.Registrationnumber,
      TermsAndConditionVerified: this.isChecked,
    };
    this.services.register(userDetails).subscribe(
      (data: any) => {
        if (data) {
          this.services.callSnackbaronSuccess(
            this.global.Registered_Successfully
          ),
            (this.registrationCompleted = true);
        } else {
          this.services.callSnackbaronError(this.global.Registration_failed);
          this.signUpBtnClicked = false;
        }
      },
      (error) => {
        if (error.error && typeof error.error == "string") {
          this.services.callSnackbaronError(error.error);
          this.signUpBtnClicked = false;
        }
      }
    );
  }

  incorrectEmail() {
    if (this.registrationCompleted) {
      this.registrationCompleted = false;
      this.signUpBtnClicked = false;
      // this.form.get('_email').reset();
      this.form.controls["_email"].reset();
      this.services.callSnackbaronWarning(
        this.global.Please_enter_your_correct_emailID
      );
    }
  }

  resendEmail() {
    let userMailID = {
      Email: this.form.value._email,
    };
    this.services.resendVerificationLink(userMailID).subscribe(
      (data: any) => {
        if (data) {
          this.services.callSnackbaronSuccess(
            this.global.Please_check_your_email_to_verify_the_link
          );
        } else {
          this.services.callSnackbaronError(this.global.Registration_failed);
        }
      },
      (error) => {
        this.services.callSnackbaronError(error.error);
      }
    );
  }
}

const matchingTwoFieldsValidator = (
  controlName: string,
  matchingControlName: string
) => {
  return async (formGroup: FormGroup) => {
    const control = formGroup.controls[controlName];
    const matchingControl = formGroup.controls[matchingControlName];
    if (matchingControl.errors && !matchingControl.errors.mustMatch) return;
    return control.value !== matchingControl.value
      ? matchingControl.setErrors({ mustMatch: true })
      : matchingControl.setErrors(null);
  };
};
