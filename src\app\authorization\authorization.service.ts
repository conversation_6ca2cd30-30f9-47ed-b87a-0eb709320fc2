import { Injectable } from "@angular/core";
import { ActivatedRoute } from '@angular/router';

@Injectable({
    providedIn: 'root'
})
export class AuthorizationService {
    clientId: any;
    userName: any;
    confirmationCode: any;
    querParams: any;
    passwordInformation: any = {};
    accessToken:string=null;
    enableResetFlag: boolean=false;
    tempPasswordInfo: any = {};
    constructor(
        private route: ActivatedRoute,
      ) {
        this.querParams =  this.route.queryParams
      .subscribe(params => {
        this.clientId = params.client_id;
        this.userName = params.user_name;
        this.confirmationCode = params.confirmation_code;
      }
      );
      }

}