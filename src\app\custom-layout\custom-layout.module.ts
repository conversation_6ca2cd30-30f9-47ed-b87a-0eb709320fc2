import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LayoutModule } from 'src/@exai/layout/layout.module';
import { CustomLayoutComponent } from './custom-layout.component';
import { SidenavModule } from 'src/@exai/layout/sidenav/sidenav.module';
import { ToolbarModule } from 'src/@exai/layout/toolbar/toolbar.module';
import { FooterModule } from 'src/@exai/layout/footer/footer.module';
import { ConfigPanelModule } from 'src/@exai/components/config-panel/config-panel.module';
import { SidebarModule } from 'src/@exai/components/sidebar/sidebar.module';
import { QuickpanelModule } from 'src/@exai/layout/quickpanel/quickpanel.module';
// C:\ui\examroom-login\src\@exai\layout\layout.module.ts
@NgModule({
  declarations: [CustomLayoutComponent],
  imports: [
    CommonModule,
    LayoutModule,
    SidenavModule,
    ToolbarModule,
    FooterModule,
    ConfigPanelModule,
    SidebarModule,
    QuickpanelModule,
  ],
})
export class CustomLayoutModule {}
